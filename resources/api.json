{"openapi": "3.0.0", "paths": {"/api/health": {"get": {"operationId": "AppController_getHealth", "parameters": [], "responses": {"200": {"description": "Returns the health status of the API", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "timestamp": {"type": "string", "example": "2025-01-09T12:00:00.000Z"}, "uptime": {"type": "number", "example": 123456}}}}}}}, "summary": "Check API health status", "tags": ["app"]}}, "/api/status": {"get": {"operationId": "AppController_getStatus", "parameters": [], "responses": {"200": {"description": "Returns detailed API status information", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "operational"}, "version": {"type": "string", "example": "1.0.0"}, "features": {"type": "array", "items": {"type": "string"}}}}}}}}, "summary": "Get API status information", "tags": ["app"]}}, "/api/users": {"post": {"operationId": "UsersController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "The user has been successfully created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "summary": "Create a new user", "tags": ["users"]}}, "/api/users/profile": {"get": {"operationId": "UsersController_getProfile", "parameters": [], "responses": {"200": {"description": "Returns the current user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get current user profile", "tags": ["users"]}, "put": {"operationId": "UsersController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "Returns the updated user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update current user profile", "tags": ["users"]}}, "/api/users/{id}": {"get": {"operationId": "UsersController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns user by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get user by ID", "tags": ["users"]}}, "/api/users/account": {"delete": {"operationId": "UsersController_deleteAccount", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAccountDto"}}}}, "responses": {"200": {"description": "Account successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Account successfully deleted"}}}}}}}, "security": [{"bearer": []}], "summary": "Soft delete user account", "tags": ["users"]}}, "/api/users/data/export": {"get": {"operationId": "UsersController_exportUserData", "parameters": [], "responses": {"200": {"description": "Returns all user data for export", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDataExportDto"}}}}}, "security": [{"bearer": []}], "summary": "Export user data for GDPR compliance", "tags": ["users"]}}, "/api/users/data/export/pdf": {"get": {"operationId": "UsersController_exportUserDataAsPDF", "parameters": [], "responses": {"200": {"description": "Returns user data as PDF file", "headers": {"Content-Type": {"description": "PDF file content type", "schema": {"type": "string", "example": "application/pdf"}}, "Content-Disposition": {"description": "File download attachment header", "schema": {"type": "string", "example": "attachment; filename=\"user-data-export.pdf\""}}}, "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}, "security": [{"bearer": []}], "summary": "Export user data as PDF document", "tags": ["users"]}}, "/api/auth/register": {"post": {"operationId": "AuthController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": "User has been successfully registered", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}, "summary": "Register a new user", "tags": ["auth"]}}, "/api/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "User has been successfully logged in", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}, "summary": "Login with email and password", "tags": ["auth"]}}, "/api/auth/social-login": {"post": {"operationId": "AuthController_socialLogin", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SocialLoginDto"}}}}, "responses": {"200": {"description": "User has been successfully logged in", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}, "summary": "Login with social providers (Google, Apple) via Firebase", "tags": ["auth"]}}, "/api/auth/forgot-password": {"post": {"operationId": "AuthController_forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "Password reset email sent if account exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "If an account with that email exists, a password reset link has been sent."}}}}}}}, "summary": "Request a password reset email", "tags": ["auth"]}}, "/api/auth/reset-password": {"post": {"operationId": "AuthController_resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "Password has been reset successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password has been reset successfully."}}}}}}}, "summary": "Reset password using 8-character code from email", "tags": ["auth"]}}, "/api/auth/resend-reset-code": {"post": {"operationId": "AuthController_resendResetCode", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "Email address to resend reset code to", "example": "<EMAIL>"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "New reset code sent if account exists and has active reset request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "A new reset code has been sent to your email address."}}}}}}}, "summary": "Resend password reset code using email address", "tags": ["auth"]}}, "/api/auth/forgot-password-firebase": {"post": {"operationId": "AuthController_forgotPasswordFirebase", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "Firebase password reset email sent if account exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "If an account with that email exists, a password reset link has been sent."}}}}}}}, "summary": "Request a password reset using Firebase (alternative method)", "tags": ["auth"]}}, "/reset-password": {"get": {"operationId": "AuthWebController_showResetPasswordForm", "parameters": [{"name": "code", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Auth<PERSON><PERSON>"]}, "post": {"operationId": "AuthWebController_handleResetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Auth<PERSON><PERSON>"]}}, "/api/habits": {"post": {"operationId": "HabitsController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHabitDto"}}}}, "responses": {"201": {"description": "The habit has been successfully created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new habit", "tags": ["habits"]}, "get": {"operationId": "HabitsController_findAll", "parameters": [], "responses": {"200": {"description": "Returns all habits for the user", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all habits", "tags": ["habits"]}}, "/api/habits/{id}": {"get": {"operationId": "HabitsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific habit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific habit", "tags": ["habits"]}, "put": {"operationId": "HabitsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHabitDto"}}}}, "responses": {"200": {"description": "Returns the updated habit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a habit", "tags": ["habits"]}}, "/api/habits/{id}/complete": {"post": {"operationId": "HabitsController_complete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the completed habit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark habit as completed", "tags": ["habits"]}}, "/api/messaging/direct": {"post": {"operationId": "MessagingController_sendDirectMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendDirectMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a direct message to a user", "tags": ["messaging"]}}, "/api/messaging/group": {"post": {"operationId": "MessagingController_sendGroupMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendGroupMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a message to a group", "tags": ["messaging"]}}, "/api/messaging/challenge": {"post": {"operationId": "MessagingController_sendChallengeMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendChallengeMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a message in a challenge", "tags": ["messaging"]}}, "/api/messaging/conversations": {"get": {"operationId": "MessagingController_getUserConversations", "parameters": [], "responses": {"200": {"description": "Returns user conversations"}}, "security": [{"bearer": []}], "summary": "Get user conversations", "tags": ["messaging"]}}, "/api/messaging/direct/{recipientId}": {"get": {"operationId": "MessagingController_getDirectMessages", "parameters": [{"name": "recipientId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns direct messages"}}, "security": [{"bearer": []}], "summary": "Get direct messages with a user", "tags": ["messaging"]}}, "/api/messaging/group/{groupId}": {"get": {"operationId": "MessagingController_getGroupMessages", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns group messages"}}, "security": [{"bearer": []}], "summary": "Get messages in a group", "tags": ["messaging"]}}, "/api/messaging/challenge/{challengeId}": {"get": {"operationId": "MessagingController_getChallengeMessages", "parameters": [{"name": "challengeId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns challenge messages"}}, "security": [{"bearer": []}], "summary": "Get messages in a challenge", "tags": ["messaging"]}}, "/api/messaging/read/{messageId}": {"put": {"operationId": "MessagingController_markMessageAsRead", "parameters": [{"name": "messageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message marked as read"}}, "security": [{"bearer": []}], "summary": "Mark a message as read", "tags": ["messaging"]}}, "/api/notifications/register-device": {"post": {"operationId": "NotificationsController_registerDevice", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDeviceDto"}}}}, "responses": {"201": {"description": "Device token registered successfully"}}, "security": [{"bearer": []}], "summary": "Register a device token for push notifications", "tags": ["notifications"]}}, "/api/notifications/preferences": {"get": {"operationId": "NotificationsController_getNotificationPreferences", "parameters": [], "responses": {"200": {"description": "Returns user notification preferences"}}, "security": [{"bearer": []}], "summary": "Get user notification preferences", "tags": ["notifications"]}, "put": {"operationId": "NotificationsController_updateNotificationPreferences", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationPreferencesDto"}}}}, "responses": {"200": {"description": "Notification preferences updated successfully"}}, "security": [{"bearer": []}], "summary": "Update user notification preferences", "tags": ["notifications"]}}, "/api/notifications/devices/{deviceId}": {"delete": {"operationId": "NotificationsController_removeDevice", "parameters": [{"name": "deviceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Device removed successfully"}}, "security": [{"bearer": []}], "summary": "Remove a device", "tags": ["notifications"]}}, "/api/notifications/test-notification": {"post": {"operationId": "NotificationsController_sendTestNotification", "parameters": [], "responses": {"200": {"description": "Test notification sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a test notification to the user's devices", "tags": ["notifications"]}}, "/api/notifications/devices": {"get": {"operationId": "NotificationsController_getUserDevices", "parameters": [], "responses": {"200": {"description": "Returns user's registered devices"}}, "security": [{"bearer": []}], "summary": "Get all registered devices for the user", "tags": ["notifications"]}}, "/api/tasks": {"post": {"operationId": "TasksController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaskDto"}}}}, "responses": {"201": {"description": "The task has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new task", "tags": ["tasks"]}, "get": {"operationId": "TasksController_findAll", "parameters": [{"name": "filter", "required": false, "in": "query", "schema": {"enum": ["all", "upcoming", "overdue"], "type": "string"}}], "responses": {"200": {"description": "Returns tasks based on filter", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get tasks based on filter", "tags": ["tasks"]}}, "/api/tasks/{id}": {"get": {"operationId": "TasksController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific task by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific task", "tags": ["tasks"]}, "put": {"operationId": "TasksController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskDto"}}}}, "responses": {"200": {"description": "Returns the updated task", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a task", "tags": ["tasks"]}, "delete": {"operationId": "TasksController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The task has been successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Task deleted successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "Delete a task", "tags": ["tasks"]}}, "/api/tasks/{id}/complete": {"post": {"operationId": "TasksController_markComplete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the completed task", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark a task as complete", "tags": ["tasks"]}}, "/api/challenges": {"post": {"operationId": "ChallengesController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChallengeDto"}}}}, "responses": {"201": {"description": "The challenge has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new challenge", "tags": ["challenges"]}, "get": {"operationId": "ChallengesController_findAll", "parameters": [{"name": "filter", "required": false, "in": "query", "schema": {"enum": ["all", "active"], "type": "string"}}], "responses": {"200": {"description": "Returns challenges based on filter", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get challenges based on filter", "tags": ["challenges"]}}, "/api/challenges/{id}": {"get": {"operationId": "ChallengesController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific challenge by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific challenge", "tags": ["challenges"]}, "put": {"operationId": "ChallengesController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChallengeDto"}}}}, "responses": {"200": {"description": "Returns the updated challenge", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a challenge", "tags": ["challenges"]}, "delete": {"operationId": "ChallengesController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The challenge has been successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Challenge deleted successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "Delete a challenge", "tags": ["challenges"]}}, "/api/challenges/{id}/join": {"post": {"operationId": "ChallengesController_join", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the challenge user joined", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Join a challenge", "tags": ["challenges"]}}, "/api/challenges/{id}/leave": {"post": {"operationId": "ChallengesController_leave", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the challenge user left", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Leave a challenge", "tags": ["challenges"]}}, "/api/challenges/{id}/progress": {"post": {"operationId": "ChallengesController_updateProgress", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgressDto"}}}}, "responses": {"200": {"description": "Returns the updated progress", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgressDto"}}}}}, "security": [{"bearer": []}], "summary": "Update challenge progress", "tags": ["challenges"]}}, "/api/skill-plans": {"post": {"operationId": "SkillPlansController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSkillPlanDto"}}}}, "responses": {"201": {"description": "The skill plan has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new skill plan", "tags": ["skill-plans"]}, "get": {"operationId": "SkillPlansController_findAll", "parameters": [{"name": "isPublic", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "creatorId", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns skill plans based on filters", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get skill plans based on filters", "tags": ["skill-plans"]}}, "/api/skill-plans/ai-generate": {"post": {"operationId": "SkillPlansController_createWithAi", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAiSkillPlanDto"}}}}, "responses": {"201": {"description": "AI-generated skill plan has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate a skill plan using AI", "tags": ["skill-plans"]}}, "/api/skill-plans/{id}": {"get": {"operationId": "SkillPlansController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific skill plan by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific skill plan", "tags": ["skill-plans"]}, "put": {"operationId": "SkillPlansController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSkillPlanDto"}}}}, "responses": {"200": {"description": "Returns the updated skill plan", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a skill plan", "tags": ["skill-plans"]}, "delete": {"operationId": "SkillPlansController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The skill plan has been successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Skill plan deleted successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "Delete a skill plan", "tags": ["skill-plans"]}}, "/api/skill-plans/{planId}/steps/{stepId}": {"put": {"operationId": "SkillPlansController_updateStep", "parameters": [{"name": "planId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "stepId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStepDto"}}}}, "responses": {"200": {"description": "Returns the updated step", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a step in a skill plan", "tags": ["skill-plans"]}}, "/api/skill-plans/{planId}/steps/{stepId}/tasks/{taskIndex}/complete": {"put": {"operationId": "SkillPlansController_markStepTaskComplete", "parameters": [{"name": "planId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "stepId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "taskIndex", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Task has been marked as complete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark a task as complete in a skill plan step", "tags": ["skill-plans"]}}, "/api/skill-plans/{id}/progress": {"post": {"operationId": "SkillPlansController_updateProgress", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStepCompletionDto"}}}}, "responses": {"200": {"description": "Returns the updated progress information", "content": {"application/json": {"schema": {"properties": {"progress": {"type": "number"}, "completedSteps": {"type": "array", "items": {"type": "number"}}}}}}}}, "security": [{"bearer": []}], "summary": "Update skill plan progress", "tags": ["skill-plans"]}}, "/api/analytics/progress": {"get": {"operationId": "AnalyticsController_getUserProgress", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for analytics", "schema": {"default": "week", "example": "week", "type": "string", "enum": ["day", "week", "month", "year"]}}], "responses": {"200": {"description": "Returns user progress analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProgressResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get user progress analytics", "tags": ["analytics"]}}, "/api/analytics/habits": {"get": {"operationId": "AnalyticsController_getHabitAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for habit analytics", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["week", "month", "year"]}}], "responses": {"200": {"description": "Returns detailed habit analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get detailed habit analytics", "tags": ["analytics"]}}, "/api/analytics/productivity": {"get": {"operationId": "AnalyticsController_getProductivityAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for productivity analytics", "schema": {"default": "week", "example": "week", "type": "string", "enum": ["week", "month"]}}], "responses": {"200": {"description": "Returns productivity analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductivityAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get productivity patterns and insights", "tags": ["analytics"]}}, "/api/analytics/mood": {"get": {"operationId": "AnalyticsController_getMoodAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for mood analytics", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["week", "month", "year"]}}], "responses": {"200": {"description": "Returns mood tracking analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MoodAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get mood tracking analytics and patterns", "tags": ["analytics"]}, "post": {"operationId": "AnalyticsController_recordMoodEntry", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MoodEntryDto"}}}}, "responses": {"201": {"description": "Mood entry has been recorded successfully"}}, "security": [{"bearer": []}], "summary": "Record a mood entry", "tags": ["analytics"]}}, "/api/analytics/insights": {"get": {"operationId": "AnalyticsController_getPersonalizedInsights", "parameters": [], "responses": {"200": {"description": "Returns personalized insights and recommendations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get personalized insights and recommendations", "tags": ["analytics"]}}, "/api/analytics/focus-session": {"post": {"operationId": "AnalyticsController_recordFocusSession", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FocusSessionDto"}}}}, "responses": {"201": {"description": "Focus session has been recorded successfully"}}, "security": [{"bearer": []}], "summary": "Record a completed focus session", "tags": ["analytics"]}}, "/api/analytics/weekly-stats": {"get": {"operationId": "AnalyticsController_getWeeklyStats", "parameters": [], "responses": {"200": {"description": "Returns aggregated weekly statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeeklyStatsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get aggregated weekly statistics", "tags": ["analytics"]}}, "/api/analytics/habit-correlations": {"get": {"operationId": "AnalyticsController_getHabitCorrelations", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for habit correlation analysis", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["month", "year"]}}], "responses": {"200": {"description": "Returns habit correlations with mood and productivity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MoodAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get habit correlations with mood and productivity", "tags": ["analytics"]}}, "/api/analytics/streak-milestones": {"get": {"operationId": "AnalyticsController_getStreakMilestones", "parameters": [], "responses": {"200": {"description": "Returns current streak milestones for habits", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get current streak milestones for habits", "tags": ["analytics"]}}, "/api/analytics/tasks": {"get": {"operationId": "AnalyticsController_getTaskCompletionStats", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for task analytics", "schema": {"default": "week", "example": "week", "type": "string", "enum": ["week", "month"]}}], "responses": {"200": {"description": "Returns task completion analytics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCompletionStatsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get task completion analytics", "tags": ["analytics"]}}, "/api/analytics/daily-progress": {"post": {"operationId": "AnalyticsController_updateDailyProgress", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDailyProgressDto"}}}}, "responses": {"201": {"description": "Daily progress has been updated successfully"}}, "security": [{"bearer": []}], "summary": "Update daily progress with XP, badges, focus time, and podcast listening", "tags": ["analytics"]}}, "/api/analytics/user-progress": {"post": {"operationId": "AnalyticsController_updateUserProgress", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserProgressDto"}}}}, "responses": {"201": {"description": "User progress has been updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProgressResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update comprehensive user progress including skill plans and achievements", "tags": ["analytics"]}}, "/api/analytics/ai-improvement-report": {"get": {"operationId": "AnalyticsController_getAIImprovementReport", "parameters": [{"name": "days", "required": false, "in": "query", "description": "Number of days to analyze for the AI improvement report", "schema": {"minimum": 7, "maximum": 365, "default": 30, "example": 30, "type": "number"}}], "responses": {"200": {"description": "Returns AI-powered lifestyle improvement report based on user data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AIImprovementReportResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get AI-powered lifestyle improvement report", "tags": ["analytics"]}}, "/api/analytics/reports/progress": {"get": {"operationId": "AnalyticsController_generateProgressReport", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for analytics", "schema": {"default": "week", "example": "week", "type": "string", "enum": ["day", "week", "month", "year"]}}], "responses": {"200": {"description": "Returns generated progress report for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProgressResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate comprehensive progress report", "tags": ["analytics"]}}, "/api/analytics/reports/habits": {"get": {"operationId": "AnalyticsController_generateHabitReport", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for habit analytics", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["week", "month", "year"]}}], "responses": {"200": {"description": "Returns generated habit report for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate detailed habit report", "tags": ["analytics"]}}, "/api/analytics/reports/productivity": {"get": {"operationId": "AnalyticsController_generateProductivityReport", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for productivity analytics", "schema": {"default": "week", "example": "week", "type": "string", "enum": ["week", "month"]}}], "responses": {"200": {"description": "Returns generated productivity report for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductivityAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate productivity report", "tags": ["analytics"]}}, "/api/analytics/reports/comprehensive": {"get": {"operationId": "AnalyticsController_generateComprehensiveReport", "parameters": [], "responses": {"200": {"description": "Returns comprehensive analytics report covering all aspects", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate comprehensive analytics report", "tags": ["analytics"]}}, "/api/podcasts/generate": {"post": {"description": "Generate a new personalized podcast using your current progress data. Requires active subscription or free trial. Users can only generate one podcast per week.", "operationId": "PodcastsController_generate", "parameters": [], "responses": {"201": {"description": "The podcast has been successfully generated based on your current progress", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate a new personalized podcast", "tags": ["podcasts"]}}, "/api/podcasts/generate/custom": {"post": {"description": "Generate a personalized podcast with optional custom mood, topic, or language preferences. Your habit, task, and progress data will still be automatically included.", "operationId": "PodcastsController_generateCustom", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPodcastPreferencesDto"}}}}, "responses": {"201": {"description": "The podcast has been successfully generated with custom preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate a personalized podcast with custom preferences", "tags": ["podcasts"]}}, "/api/podcasts/weekly": {"get": {"description": "Get the weekly personalized podcast. Requires active subscription or free trial.", "operationId": "PodcastsController_getWeeklyPodcast", "parameters": [], "responses": {"200": {"description": "Returns the weekly personalized podcast", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get weekly personalized podcast", "tags": ["podcasts"]}}, "/api/podcasts/history": {"get": {"operationId": "PodcastsController_getHistory", "parameters": [], "responses": {"200": {"description": "Returns the list of listened podcasts", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get podcast listening history", "tags": ["podcasts"]}}, "/api/podcasts/{id}/listened": {"post": {"operationId": "PodcastsController_markAsListened", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the updated podcast marked as listened", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark a podcast as listened", "tags": ["podcasts"]}}, "/api/podcasts": {"get": {"operationId": "PodcastsController_findAll", "parameters": [], "responses": {"200": {"description": "Returns all podcasts for the current user", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all podcasts for current user", "tags": ["podcasts"]}}, "/api/podcasts/{id}": {"get": {"operationId": "PodcastsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific podcast by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific podcast", "tags": ["podcasts"]}, "delete": {"operationId": "PodcastsController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The podcast has been successfully deleted"}}, "security": [{"bearer": []}], "summary": "Delete a podcast", "tags": ["podcasts"]}}, "/api/monetization/plans": {"get": {"operationId": "MonetizationController_getPlans", "parameters": [{"name": "platform", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of active subscription plans"}}, "summary": "Get all active subscription plans", "tags": ["Monetization"]}}, "/api/monetization/plans/{id}": {"get": {"operationId": "MonetizationController_getPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan details"}}, "summary": "Get subscription plan by ID", "tags": ["Monetization"]}}, "/api/monetization/subscribe": {"post": {"operationId": "MonetizationController_subscribe", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionDto"}}}}, "responses": {"201": {"description": "Subscription created successfully"}}, "security": [{"bearer": []}], "summary": "Create a new subscription", "tags": ["Monetization"]}}, "/api/monetization/my-subscriptions": {"get": {"operationId": "MonetizationController_getMySubscriptions", "parameters": [], "responses": {"200": {"description": "List of user subscriptions"}}, "security": [{"bearer": []}], "summary": "Get user subscriptions", "tags": ["Monetization"]}}, "/api/monetization/subscription-status": {"get": {"operationId": "MonetizationController_getSubscriptionStatus", "parameters": [], "responses": {"200": {"description": "Current subscription status"}}, "security": [{"bearer": []}], "summary": "Get current subscription status", "tags": ["Monetization"]}}, "/api/monetization/subscriptions/{id}": {"patch": {"operationId": "MonetizationController_updateSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionDto"}}}}, "responses": {"200": {"description": "Subscription updated successfully"}}, "security": [{"bearer": []}], "summary": "Update subscription", "tags": ["Monetization"]}}, "/api/monetization/subscriptions/{id}/cancel": {"post": {"operationId": "MonetizationController_cancelSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription cancelled successfully"}}, "security": [{"bearer": []}], "summary": "Cancel subscription", "tags": ["Monetization"]}}, "/api/monetization/verify/apple-receipt": {"post": {"operationId": "MonetizationController_verifyAppleReceipt", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyReceiptDto"}}}}, "responses": {"200": {"description": "Receipt verified successfully"}}, "security": [{"bearer": []}], "summary": "Verify Apple App Store receipt", "tags": ["Monetization"]}}, "/api/monetization/verify/google-purchase": {"post": {"operationId": "MonetizationController_verifyGooglePurchase", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPurchaseTokenDto"}}}}, "responses": {"200": {"description": "Purchase verified successfully"}}, "security": [{"bearer": []}], "summary": "Verify Google Play purchase", "tags": ["Monetization"]}}, "/api/monetization/validate-coupon": {"post": {"operationId": "MonetizationController_validateCoupon", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateCouponDto"}}}}, "responses": {"200": {"description": "Coupon validation result"}}, "security": [{"bearer": []}], "summary": "Validate coupon code", "tags": ["Monetization"]}}, "/api/admin/monetization/plans": {"get": {"operationId": "MonetizationAdminController_getAllPlans", "parameters": [], "responses": {"200": {"description": "List of all subscription plans"}}, "security": [{"bearer": []}], "summary": "Get all subscription plans (admin)", "tags": ["Admin - Monetization"]}, "post": {"operationId": "MonetizationAdminController_createPlan", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionPlanDto"}}}}, "responses": {"201": {"description": "Subscription plan created successfully"}}, "security": [{"bearer": []}], "summary": "Create new subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/plans/{id}": {"patch": {"operationId": "MonetizationAdminController_updatePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionPlanDto"}}}}, "responses": {"200": {"description": "Subscription plan updated successfully"}}, "security": [{"bearer": []}], "summary": "Update subscription plan", "tags": ["Admin - Monetization"]}, "delete": {"operationId": "MonetizationAdminController_deletePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/plans/{id}/activate": {"post": {"operationId": "MonetizationAdminController_activatePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan activated successfully"}}, "security": [{"bearer": []}], "summary": "Activate subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/plans/{id}/deactivate": {"post": {"operationId": "MonetizationAdminController_deactivatePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan deactivated successfully"}}, "security": [{"bearer": []}], "summary": "Deactivate subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/subscriptions": {"get": {"operationId": "MonetizationAdminController_getAllSubscriptions", "parameters": [], "responses": {"200": {"description": "List of all subscriptions"}}, "security": [{"bearer": []}], "summary": "Get all subscriptions (admin)", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/subscriptions/{id}": {"get": {"operationId": "MonetizationAdminController_getSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription details"}}, "security": [{"bearer": []}], "summary": "Get subscription by ID (admin)", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/subscriptions/{id}/cancel": {"post": {"operationId": "MonetizationAdminController_cancelSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription cancelled successfully"}}, "security": [{"bearer": []}], "summary": "Cancel subscription (admin)", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons": {"get": {"operationId": "MonetizationAdminController_getAllCoupons", "parameters": [], "responses": {"200": {"description": "List of all coupon codes"}}, "security": [{"bearer": []}], "summary": "Get all coupon codes (admin)", "tags": ["Admin - Monetization"]}, "post": {"operationId": "MonetizationAdminController_createCoupon", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCouponCodeDto"}}}}, "responses": {"201": {"description": "Coupon code created successfully"}}, "security": [{"bearer": []}], "summary": "Create new coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}": {"get": {"operationId": "MonetizationAdminController_getCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code details"}}, "security": [{"bearer": []}], "summary": "Get coupon code by ID (admin)", "tags": ["Admin - Monetization"]}, "patch": {"operationId": "MonetizationAdminController_updateCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCouponCodeDto"}}}}, "responses": {"200": {"description": "Coupon code updated successfully"}}, "security": [{"bearer": []}], "summary": "Update coupon code", "tags": ["Admin - Monetization"]}, "delete": {"operationId": "MonetizationAdminController_deleteCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}/activate": {"post": {"operationId": "MonetizationAdminController_activateCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code activated successfully"}}, "security": [{"bearer": []}], "summary": "Activate coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}/deactivate": {"post": {"operationId": "MonetizationAdminController_deactivateCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code deactivated successfully"}}, "security": [{"bearer": []}], "summary": "Deactivate coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}/stats": {"get": {"operationId": "MonetizationAdminController_getCouponStats", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code usage statistics"}}, "security": [{"bearer": []}], "summary": "Get coupon code usage statistics", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/analytics/overview": {"get": {"operationId": "MonetizationAdminController_getAnalyticsOverview", "parameters": [], "responses": {"200": {"description": "Monetization analytics overview"}}, "security": [{"bearer": []}], "summary": "Get monetization overview analytics", "tags": ["Admin - Monetization"]}}, "/api/webhooks/app-store": {"post": {"operationId": "WebhookController_handleAppStoreWebhook", "parameters": [], "responses": {"200": {"description": "Webhook processed successfully"}, "400": {"description": "Invalid webhook payload"}}, "summary": "Handle App Store webhook notifications", "tags": ["webhooks"]}}, "/api/webhooks/google-play": {"post": {"operationId": "WebhookController_handleGooglePlayWebhook", "parameters": [], "responses": {"200": {"description": "Webhook processed successfully"}, "400": {"description": "Invalid webhook payload"}}, "summary": "Handle Google Play webhook notifications", "tags": ["webhooks"]}}, "/podcasts/{filename}": {"get": {"operationId": "StaticFilesController_servePodcastFile", "parameters": [{"name": "filename", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["StaticFiles"]}}, "/api/webhooks/firebase/auth": {"post": {"operationId": "FirebaseWebhooksController_handleAuthWebhook", "parameters": [], "requestBody": {"required": true, "description": "Firebase Auth webhook payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirebaseWebhookDto"}, "examples": {"userCreated": {"value": {"event": {"type": "user.created", "data": {"uid": "firebase-uid", "email": "<EMAIL>", "metadata": {"createdAt": "2023-05-20T12:00:00.000Z"}}}}}, "userDeleted": {"value": {"event": {"type": "user.deleted", "data": {"uid": "firebase-uid"}}}}}}}}, "responses": {"201": {"description": ""}}, "summary": "Handle Firebase Authentication webhooks", "tags": ["webhooks"]}}, "/api/help/contact": {"post": {"operationId": "HelpController_submitContactInquiry", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactUsDto"}}}}, "responses": {"201": {"description": "Contact inquiry submitted successfully"}}, "summary": "Submit a contact inquiry", "tags": ["help"]}}, "/api/help/contacts": {"get": {"operationId": "HelpController_getContactInquiries", "parameters": [{"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get contact inquiries (Admin only)", "tags": ["help"]}}, "/api/help/contact/{id}": {"get": {"operationId": "HelpController_getContactInquiry", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Contact inquiry ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get a specific contact inquiry (Admin only)", "tags": ["help"]}}, "/api/help/contact/{id}/status": {"patch": {"operationId": "HelpController_updateContactInquiryStatus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Contact inquiry ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Update contact inquiry status (Admin only)", "tags": ["help"]}}, "/api/help/feedback": {"post": {"operationId": "HelpController_submitFeedback", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeedbackDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> submitted successfully"}}, "summary": "Submit feedback", "tags": ["help"]}, "get": {"operationId": "HelpController_getFeedback", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get feedback submissions (Admin only)", "tags": ["help"]}}, "/api/help/feedback/{id}": {"get": {"operationId": "HelpController_getFeedbackById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get specific feedback (Admin only)", "tags": ["help"]}}, "/api/help/feedback/{id}/status": {"patch": {"operationId": "HelpController_updateFeedbackStatus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Update feedback status (Admin only)", "tags": ["help"]}}, "/api/help/articles/search": {"get": {"operationId": "HelpController_searchHelpArticles", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"example": "how to reset password", "type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of results", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Results offset", "schema": {"default": 0, "example": 0, "type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "Search help articles", "tags": ["help"]}}, "/api/help/articles/popular": {"get": {"operationId": "HelpController_getPopularArticles", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of articles to return", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "Get popular help articles", "tags": ["help"]}}, "/api/help/articles/faq": {"get": {"operationId": "HelpController_getFAQs", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get frequently asked questions", "tags": ["help"]}}, "/api/help/articles/category/{category}": {"get": {"operationId": "HelpController_getHelpArticlesByCategory", "parameters": [{"name": "category", "required": true, "in": "path", "description": "Article category", "schema": {"enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"], "type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get help articles by category", "tags": ["help"]}}, "/api/help/articles/{id}": {"get": {"operationId": "HelpController_getHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Article ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get a specific help article", "tags": ["help"]}}, "/api/help/articles/{id}/helpful": {"post": {"operationId": "HelpController_markArticleHelpful", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Article ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"helpful": {"type": "boolean"}}}}}}, "responses": {"201": {"description": ""}}, "summary": "Mark article as helpful or not helpful", "tags": ["help"]}}, "/api/help/privacy-policy": {"get": {"operationId": "HelpController_getPrivacyPolicy", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get privacy policy content", "tags": ["help"]}}, "/api/help/terms-and-conditions": {"get": {"operationId": "HelpController_getTermsAndConditions", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get terms and conditions content", "tags": ["help"]}}, "/api/help/about-us": {"get": {"operationId": "HelpController_getAboutUs", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get about us information", "tags": ["help"]}}, "/admin": {"get": {"operationId": "AdminWebController_adminRoot", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/login": {"get": {"operationId": "AdminWebController_loginPage", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_login", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/logout": {"post": {"operationId": "AdminWebController_logout", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/dashboard": {"get": {"operationId": "AdminWebController_dashboard", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/landing-page": {"get": {"operationId": "AdminWebController_landingPageManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/landing-page/{section}": {"post": {"operationId": "AdminWebController_updateLandingPageContent", "parameters": [{"name": "section", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/app-constants": {"get": {"operationId": "AdminWebController_appConstantsManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_updateAppConstants", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help": {"get": {"operationId": "AdminWebController_helpManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createHelpArticle", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help/new": {"get": {"operationId": "AdminWebController_newHelpArticle", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help/{id}/edit": {"get": {"operationId": "AdminWebController_editHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help/{id}": {"post": {"operationId": "AdminWebController_updateHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/community": {"get": {"operationId": "AdminWebController_communityManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/community/challenges/{id}/edit": {"get": {"operationId": "AdminWebController_editChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/community/challenges/{id}": {"post": {"operationId": "AdminWebController_updateChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/blog": {"get": {"operationId": "AdminWebController_blogManagement", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createBlogPost", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/blog/{id}": {"post": {"operationId": "AdminWebController_updateBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/api/blog": {"get": {"operationId": "AdminWebController_apiBlogGetAll", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_apiBlogCreate", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/api/blog/{id}": {"get": {"operationId": "AdminWebController_apiBlogGetOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "put": {"operationId": "AdminWebController_apiBlogUpdate", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "delete": {"operationId": "AdminWebController_apiBlogDelete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization": {"get": {"operationId": "AdminWebController_monetizationDashboard", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans": {"get": {"operationId": "AdminWebController_monetizationPlans", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createMonetizationPlan", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/new": {"get": {"operationId": "AdminWebController_newMonetizationPlan", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/{id}/edit": {"get": {"operationId": "AdminWebController_editMonetizationPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/{id}": {"post": {"operationId": "AdminWebController_updateMonetizationPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/{id}/delete": {"post": {"operationId": "AdminWebController_deleteMonetizationPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/subscriptions": {"get": {"operationId": "AdminWebController_monetizationSubscriptions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/subscriptions/{id}": {"get": {"operationId": "AdminWebController_showMonetizationSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/subscriptions/{id}/cancel": {"post": {"operationId": "AdminWebController_cancelMonetizationSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons": {"get": {"operationId": "AdminWebController_monetizationCoupons", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createMonetizationCoupon", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/new": {"get": {"operationId": "AdminWebController_newMonetizationCoupon", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/edit": {"get": {"operationId": "AdminWebController_editMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}": {"post": {"operationId": "AdminWebController_updateMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/delete": {"post": {"operationId": "AdminWebController_deleteMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/activate": {"post": {"operationId": "AdminWebController_activateMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/deactivate": {"post": {"operationId": "AdminWebController_deactivateMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/analytics": {"get": {"operationId": "AdminWebController_monetizationAnalytics", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/analytics/export/csv": {"get": {"operationId": "AdminWebController_exportAnalyticsCSV", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/analytics/export/report": {"get": {"operationId": "AdminWebController_generateAnalyticsReport", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/api/admin/dashboard/stats": {"get": {"operationId": "AdminController_getDashboardStats", "parameters": [], "responses": {"200": {"description": "Returns dashboard statistics"}}, "security": [{"bearer": []}], "summary": "Get dashboard statistics", "tags": ["admin"]}}, "/api/admin/landing-page": {"get": {"operationId": "AdminController_getLandingPageContent", "parameters": [{"name": "section", "required": false, "in": "query", "description": "Specific section to retrieve", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns landing page content"}}, "security": [{"bearer": []}], "summary": "Get all landing page content sections", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateAllLandingPageContent", "parameters": [], "responses": {"200": {"description": "Landing page content updated successfully"}}, "security": [{"bearer": []}], "summary": "Update all landing page content at once", "tags": ["admin"]}}, "/api/admin/landing-page/{section}": {"put": {"operationId": "AdminController_updateLandingPageContent", "parameters": [{"name": "section", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Landing page content updated successfully"}}, "security": [{"bearer": []}], "summary": "Update landing page content for a specific section", "tags": ["admin"]}}, "/api/admin/app-constants": {"get": {"operationId": "AdminController_getAppConstants", "parameters": [], "responses": {"200": {"description": "Returns app constants"}}, "security": [{"bearer": []}], "summary": "Get app constants", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateAppConstants", "parameters": [], "responses": {"200": {"description": "App constants updated successfully"}}, "security": [{"bearer": []}], "summary": "Update app constants", "tags": ["admin"]}}, "/api/admin/help": {"get": {"operationId": "AdminController_getHelpArticles", "parameters": [], "responses": {"200": {"description": "Returns all help articles", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all help articles", "tags": ["admin"]}, "post": {"operationId": "AdminController_createHelpArticle", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHelpArticleDto"}}}}, "responses": {"201": {"description": "Help article created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create new help article", "tags": ["admin"]}}, "/api/admin/help/{id}": {"get": {"operationId": "AdminController_getHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns help article", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get help article by ID", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHelpArticleDto"}}}}, "responses": {"200": {"description": "Help article updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update help article", "tags": ["admin"]}, "delete": {"operationId": "AdminController_deleteHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Help article deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete help article", "tags": ["admin"]}}, "/api/admin/community": {"get": {"operationId": "AdminController_getCommunityData", "parameters": [], "responses": {"200": {"description": "Returns community data and statistics"}}, "security": [{"bearer": []}], "summary": "Get community data overview", "tags": ["admin"]}}, "/api/admin/community/challenges": {"get": {"operationId": "AdminController_getChallenges", "parameters": [], "responses": {"200": {"description": "Returns all challenges"}}, "security": [{"bearer": []}], "summary": "Get all challenges", "tags": ["admin"]}}, "/api/admin/community/challenges/{id}": {"get": {"operationId": "AdminController_getChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns challenge details"}}, "security": [{"bearer": []}], "summary": "Get challenge by ID", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Challenge updated successfully"}}, "security": [{"bearer": []}], "summary": "Update challenge", "tags": ["admin"]}, "delete": {"operationId": "AdminController_deleteChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Challenge deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete challenge", "tags": ["admin"]}}, "/api/admin/blog/stats": {"get": {"operationId": "AdminController_getBlogStats", "parameters": [], "responses": {"200": {"description": "Returns blog statistics"}}, "security": [{"bearer": []}], "summary": "Get blog statistics", "tags": ["admin"]}}, "/api/admin/blog": {"get": {"operationId": "AdminController_getAllBlogPosts", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns all blog posts"}}, "security": [{"bearer": []}], "summary": "Get all blog posts", "tags": ["admin"]}, "post": {"operationId": "AdminController_createBlogPost", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}}}, "responses": {"201": {"description": "Blog post created successfully"}}, "security": [{"bearer": []}], "summary": "Create new blog post", "tags": ["admin"]}}, "/api/admin/blog/{id}": {"get": {"operationId": "AdminController_getBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns blog post"}}, "security": [{"bearer": []}], "summary": "Get blog post by ID", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogPostDto"}}}}, "responses": {"200": {"description": "Blog post updated successfully"}}, "security": [{"bearer": []}], "summary": "Update blog post", "tags": ["admin"]}, "delete": {"operationId": "AdminController_deleteBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Blog post deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete blog post", "tags": ["admin"]}}, "/admin/api/upload/image": {"post": {"operationId": "UploadController_uploadImage", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Upload"]}}, "/admin/api/upload/image-by-url": {"post": {"operationId": "UploadController_uploadImageByUrl", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Upload"]}}, "/api/blog": {"get": {"operationId": "BlogController_getAllPosts", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns paginated blog posts", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}}}}}, "summary": "Get all published blog posts", "tags": ["blog-api"]}}, "/api/blog/featured": {"get": {"operationId": "BlogController_getFeaturedPosts", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"example": 5, "type": "number"}}], "responses": {"200": {"description": "Returns featured blog posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}, "total": {"type": "number"}}}}}}}, "summary": "Get featured blog posts", "tags": ["blog-api"]}}, "/api/blog/category/{category}": {"get": {"operationId": "BlogController_getPostsByCategory", "parameters": [{"name": "category", "required": true, "in": "path", "description": "Blog category", "schema": {"enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "Returns blog posts by category", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}, "total": {"type": "number"}}}}}}}, "summary": "Get blog posts by category", "tags": ["blog-api"]}}, "/api/blog/{slug}": {"get": {"operationId": "BlogController_getPostBySlug", "parameters": [{"name": "slug", "required": true, "in": "path", "description": "Blog post slug", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the blog post", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlogPostResponseDto"}}}}, "404": {"description": "Blog post not found"}}, "summary": "Get a blog post by slug", "tags": ["blog-api"]}}, "/api/blog/{id}/related": {"get": {"operationId": "BlogController_getRelatedPosts", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of related posts to return", "schema": {"example": 3, "type": "number"}}], "responses": {"200": {"description": "Returns related blog posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}, "total": {"type": "number"}}}}}}}, "summary": "Get related blog posts", "tags": ["blog-api"]}}, "/api/calendar": {"post": {"operationId": "CalendarController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCalendarEventDto"}}}}, "responses": {"201": {"description": "Calendar event created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Create a new calendar event", "tags": ["calendar"]}, "get": {"operationId": "CalendarController_findAll", "parameters": [{"name": "startDate", "required": false, "in": "query", "description": "Start date to filter events (ISO 8601 format)", "schema": {"example": "2025-06-01T00:00:00Z", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "End date to filter events (ISO 8601 format)", "schema": {"example": "2025-06-30T23:59:59Z", "type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "Filter by event type", "schema": {"type": "string", "enum": ["habit", "task", "custom"]}}], "responses": {"200": {"description": "Calendar events retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get all calendar events for the authenticated user", "tags": ["calendar"]}}, "/api/calendar/{id}": {"get": {"operationId": "CalendarController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Calendar event retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Get a specific calendar event by ID", "tags": ["calendar"]}, "patch": {"operationId": "CalendarController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCalendarEventDto"}}}}, "responses": {"200": {"description": "Calendar event updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Update a calendar event", "tags": ["calendar"]}, "delete": {"operationId": "CalendarController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Calendar event deleted successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Delete a calendar event", "tags": ["calendar"]}}, "/api/calendar/{id}/toggle-complete": {"patch": {"operationId": "CalendarController_toggleComplete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Calendar event completion status toggled successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Toggle completion status of a calendar event", "tags": ["calendar"]}}, "/api/feedback": {"post": {"operationId": "FeedbackController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMobileFeedbackDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Submit mobile app feedback", "tags": ["Mobile Feedback"]}, "get": {"operationId": "FeedbackController_findAll", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search in title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns paginated feedback list", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all feedback (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/anonymous": {"post": {"operationId": "FeedbackController_createAnonymous", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMobileFeedbackDto"}}}}, "responses": {"201": {"description": "Anonymous feedback submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "summary": "Submit anonymous feedback (no authentication required)", "tags": ["Mobile Feedback"]}}, "/api/feedback/my-feedback": {"get": {"operationId": "FeedbackController_findMyFeedback", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns user's feedback list", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get current user's feedback", "tags": ["Mobile Feedback"]}}, "/api/feedback/stats": {"get": {"operationId": "FeedbackController_getStats", "parameters": [], "responses": {"200": {"description": "Returns feedback statistics"}}, "security": [{"bearer": []}], "summary": "Get feedback statistics (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/priority-stats": {"get": {"operationId": "FeedbackController_getPriorityStats", "parameters": [], "responses": {"200": {"description": "Returns priority statistics"}}, "security": [{"bearer": []}], "summary": "Get priority distribution statistics (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/top-feature-requests": {"get": {"operationId": "FeedbackController_getTopFeatureRequests", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of feature requests to return", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns top feature requests", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get top feature requests (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/{id}": {"get": {"operationId": "FeedbackController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns feedback details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get feedback by ID", "tags": ["Mobile Feedback"]}, "patch": {"operationId": "FeedbackController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMobileFeedbackDto"}}}}, "responses": {"200": {"description": "Feedback updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update feedback (Admin only)", "tags": ["Mobile Feedback"]}, "delete": {"operationId": "FeedbackController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"204": {"description": "Feedback deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete feedback (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/{id}/resolve": {"patch": {"operationId": "FeedbackController_markAsResolved", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"resolutionNotes": {"type": "string", "description": "Optional resolution notes"}}}}}}, "responses": {"200": {"description": "Feedback marked as resolved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark feedback as resolved (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/{id}/assign": {"patch": {"operationId": "FeedbackController_assignToAdmin", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"adminId": {"type": "string", "description": "Admin user ID"}}, "required": ["adminId"]}}}}, "responses": {"200": {"description": "Fe<PERSON><PERSON> assigned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Assign feedback to admin (Admin only)", "tags": ["Mobile Feedback"]}}, "/admin/feedback": {"get": {"operationId": "FeedbackAdminWebController_index", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/stats": {"get": {"operationId": "FeedbackAdminWebController_stats", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}": {"get": {"operationId": "FeedbackAdminWebController_show", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}, "post": {"operationId": "FeedbackAdminWebController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/edit": {"get": {"operationId": "FeedbackAdminWebController_edit", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/assign": {"post": {"operationId": "FeedbackAdminWebController_assign", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/resolve": {"post": {"operationId": "FeedbackAdminWebController_resolve", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/delete": {"post": {"operationId": "FeedbackAdminWebController_delete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/export/csv": {"get": {"operationId": "FeedbackAdminWebController_exportCsv", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}}, "info": {"title": "Power Up API", "description": "The Power Up API description", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateUserDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "StrongPassword123", "minLength": 6}, "firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}}, "required": ["email", "password", "firstName", "lastName"]}, "UserResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique user ID", "example": "123e4567-e89b-12d3-a456-************"}, "email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}, "xp": {"type": "number", "description": "User experience points", "example": 150}, "badges": {"description": "User badges earned", "example": ["early-bird", "consistent-performer"], "type": "array", "items": {"type": "string"}}, "provider": {"type": "string", "description": "Authentication provider", "enum": ["local", "google", "apple"], "example": "local"}, "picture": {"type": "string", "description": "User profile picture URL", "example": "https://example.com/avatar.jpg", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "User creation date", "example": "2025-05-19T10:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "User last update date", "example": "2025-05-19T10:00:00Z"}}, "required": ["id", "email", "firstName", "lastName", "xp", "badges", "provider", "picture", "createdAt", "updatedAt"]}, "UpdateUserDto": {"type": "object", "properties": {"firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}}}, "DeleteAccountDto": {"type": "object", "properties": {"password": {"type": "string", "description": "Password confirmation for account deletion", "minLength": 6}, "reason": {"type": "string", "description": "Optional reason for deleting the account"}}, "required": ["password"]}, "UserDataExportDto": {"type": "object", "properties": {"profile": {"type": "object", "description": "User profile information"}, "habits": {"description": "User habits data", "type": "array", "items": {"type": "string"}}, "tasks": {"description": "User tasks data", "type": "array", "items": {"type": "string"}}, "exportedAt": {"format": "date-time", "type": "string", "description": "Data export timestamp"}}, "required": ["profile", "habits", "tasks", "exportedAt"]}, "RegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "StrongPassword123", "minLength": 6}, "firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}}, "required": ["email", "password", "firstName", "lastName"]}, "AuthResponseDto": {"type": "object", "properties": {"access_token": {"type": "string", "description": "JWT access token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "user": {"type": "object", "description": "User info", "example": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}}}, "required": ["access_token", "user"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "StrongPassword123"}}, "required": ["email", "password"]}, "SocialLoginDto": {"type": "object", "properties": {"idToken": {"type": "string", "description": "Firebase ID token from social authentication", "example": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjFlOTczZWUwZTE2ZjdlZWY5NDY5MDdhMDYyYjZhODQyZGQzYzdiMTAiLCJ0eXAiOiJKV1QifQ..."}}, "required": ["idToken"]}, "ForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address to send password reset link", "example": "<EMAIL>"}}, "required": ["email"]}, "ResetPasswordDto": {"type": "object", "properties": {"code": {"type": "string", "description": "8-character reset code received in email", "example": "ABC12345"}, "newPassword": {"type": "string", "description": "New password", "example": "NewStrongPassword123", "minLength": 6}}, "required": ["code", "newPassword"]}, "ReminderSettingsDto": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether reminders are enabled for this habit", "example": true}, "time": {"type": "string", "description": "Time of day for the reminder in HH:MM format", "example": "08:00"}, "days": {"description": "Days of the week for the reminder", "example": ["Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}}, "required": ["enabled", "time", "days"]}, "CreateHabitDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the habit", "example": "Morning Meditation"}, "description": {"type": "string", "description": "Description of the habit", "example": "A 10-minute meditation practice every morning to start the day focused"}, "schedule": {"description": "Days or frequency of the habit", "example": ["daily", "weekdays", "Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}, "reminderSettings": {"description": "Reminder settings for the habit", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}, "required": ["name", "schedule"]}, "ReminderSettingsResponseDto": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether reminder is enabled", "example": true}, "time": {"type": "string", "description": "Time for the reminder", "example": "08:00"}, "days": {"description": "Days for the reminder", "example": ["Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}}, "required": ["enabled", "time", "days"]}, "HabitResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique habit ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Habit name", "example": "Morning Meditation"}, "description": {"type": "string", "description": "Habit description", "example": "A 10-minute mindfulness practice", "nullable": true}, "schedule": {"description": "Schedule (days of week)", "example": ["Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}, "currentStreak": {"type": "number", "description": "Current streak count", "example": 5}, "longestStreak": {"type": "number", "description": "Longest streak achieved", "example": 12}, "completion": {"type": "object", "description": "Completion record by date", "example": {"2025-06-01": true, "2025-06-02": false}}, "xpReward": {"type": "number", "description": "Experience points reward for completing habit", "example": 10}, "lastCompletedAt": {"format": "date-time", "type": "string", "description": "Date when habit was last completed", "example": "2025-06-01", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "Habit creation date", "example": "2025-05-19T10:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Habit last update date", "example": "2025-05-19T10:00:00Z"}, "reminderSettings": {"description": "Reminder settings", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ReminderSettingsResponseDto"}]}}, "required": ["id", "name", "description", "schedule", "currentStreak", "longestStreak", "completion", "xpReward", "lastCompletedAt", "createdAt", "updatedAt", "reminderSettings"]}, "UpdateHabitDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the habit", "example": "Morning Meditation"}, "description": {"type": "string", "description": "Description of the habit", "example": "A 10-minute meditation practice every morning to start the day focused"}, "schedule": {"description": "Days or frequency of the habit", "example": ["daily", "weekdays", "Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}, "reminderSettings": {"description": "Reminder settings for the habit", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}}, "SendDirectMessageDto": {"type": "object", "properties": {"recipientId": {"type": "string", "description": "ID of the recipient user", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "Message content", "example": "Hello, how are you doing today?"}, "attachments": {"description": "Optional attachment URLs", "example": ["https://example.com/image.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["recipientId", "content"]}, "SendGroupMessageDto": {"type": "object", "properties": {"groupId": {"type": "string", "description": "ID of the group", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "Message content", "example": "Hello everyone! How is the challenge going?"}, "attachments": {"description": "Optional attachment URLs", "example": ["https://example.com/image.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["groupId", "content"]}, "SendChallengeMessageDto": {"type": "object", "properties": {"challengeId": {"type": "string", "description": "ID of the challenge", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "Message content", "example": "Just completed my daily goal for the challenge!"}, "attachments": {"description": "Optional attachment URLs", "example": ["https://example.com/image.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["challengeId", "content"]}, "RegisterDeviceDto": {"type": "object", "properties": {"deviceToken": {"type": "string", "description": "Device token for push notifications", "example": "exampleDeviceToken123456789"}, "deviceType": {"type": "string", "description": "Device type", "example": "ios", "enum": ["ios", "android", "web"]}, "deviceName": {"type": "string", "description": "Device name", "example": "<PERSON>'s iPhone"}}, "required": ["deviceToken", "deviceType"]}, "UpdateNotificationPreferencesDto": {"type": "object", "properties": {"taskReminders": {"type": "boolean", "description": "Enable/disable task reminder notifications", "example": true}, "habitReminders": {"type": "boolean", "description": "Enable/disable habit reminder notifications", "example": true}, "streakAlerts": {"type": "boolean", "description": "Enable/disable streak alert notifications", "example": true}, "milestoneCelebrations": {"type": "boolean", "description": "Enable/disable milestone celebration notifications", "example": true}, "challengeUpdates": {"type": "boolean", "description": "Enable/disable challenge update notifications", "example": true}, "newMessages": {"type": "boolean", "description": "Enable/disable new message notifications", "example": true}, "podcastReady": {"type": "boolean", "description": "Enable/disable podcast ready notifications", "example": true}}}, "CreateTaskDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Task title", "example": "Complete project proposal"}, "description": {"type": "string", "description": "Task description", "example": "Write up the proposal with timeline and budget"}, "dueDate": {"format": "date-time", "type": "string", "description": "Task due date", "example": "2025-05-25T10:00:00Z"}, "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "example": "medium"}, "reminderSettings": {"description": "Task reminder settings", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}, "required": ["title", "dueDate", "priority"]}, "TaskResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique task ID", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Task title", "example": "Complete project proposal"}, "description": {"type": "string", "description": "Task description", "example": "Write up the proposal with timeline and budget", "nullable": true}, "dueDate": {"format": "date-time", "type": "string", "description": "Task due date", "example": "2025-05-25T10:00:00Z"}, "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "example": "medium"}, "completed": {"type": "boolean", "description": "Whether task is completed", "example": false}, "completedAt": {"format": "date-time", "type": "string", "description": "Date when task was completed", "example": "2025-05-25T14:30:00Z", "nullable": true}, "notifiedOverdue": {"type": "boolean", "description": "Whether user has been notified about overdue task", "example": false}, "createdAt": {"format": "date-time", "type": "string", "description": "Task creation date", "example": "2025-05-19T10:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Task last update date", "example": "2025-05-19T10:00:00Z"}, "reminderSettings": {"type": "object", "description": "Task reminder settings", "nullable": true, "example": {"enabled": true, "time": "09:00"}}}, "required": ["id", "title", "description", "dueDate", "priority", "completed", "completedAt", "notifiedOverdue", "createdAt", "updatedAt", "reminderSettings"]}, "UpdateTaskDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Task title", "example": "Complete project proposal"}, "description": {"type": "string", "description": "Task description", "example": "Write up the proposal with timeline and budget"}, "dueDate": {"format": "date-time", "type": "string", "description": "Task due date", "example": "2025-05-25T10:00:00Z"}, "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "example": "medium"}, "reminderSettings": {"description": "Task reminder settings", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}}, "ChallengeRewardsDto": {"type": "object", "properties": {"xp": {"type": "number", "description": "Experience points reward", "example": 500}, "badges": {"description": "Badges to be rewarded", "example": ["Challenge Champion", "Early Bird"], "type": "array", "items": {"type": "string"}}}, "required": ["xp", "badges"]}, "ChallengeRulesDto": {"type": "object", "properties": {"goals": {"description": "Challenge goals", "example": ["Complete 10 workouts", "Meditate for 20 days"], "type": "array", "items": {"type": "string"}}, "requirements": {"description": "Challenge requirements", "example": ["Log activity daily", "Share progress weekly"], "type": "array", "items": {"type": "string"}}, "rewards": {"description": "Challenge rewards", "allOf": [{"$ref": "#/components/schemas/ChallengeRewardsDto"}]}}, "required": ["goals", "requirements", "rewards"]}, "CreateChallengeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Challenge name", "example": "Summer Fitness Challenge"}, "description": {"type": "string", "description": "Challenge description", "example": "A 30-day challenge to improve your fitness and well-being"}, "startDate": {"format": "date-time", "type": "string", "description": "Challenge start date", "example": "2025-06-01T00:00:00Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Challenge end date", "example": "2025-06-30T23:59:59Z"}, "rules": {"description": "Challenge rules", "allOf": [{"$ref": "#/components/schemas/ChallengeRulesDto"}]}}, "required": ["name", "description", "startDate", "endDate", "rules"]}, "ChallengeRules": {"type": "object", "properties": {"goals": {"description": "Challenge goals", "example": ["Complete 10 workouts", "Meditate for 20 days"], "type": "array", "items": {"type": "string"}}, "requirements": {"description": "Challenge requirements", "example": ["Log activity daily", "Share progress weekly"], "type": "array", "items": {"type": "string"}}, "rewards": {"type": "object", "description": "Challenge rewards", "example": {"xp": 500, "badges": ["Challenge Champion", "Early Bird"]}}}, "required": ["goals", "requirements", "rewards"]}, "ChallengeResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique challenge ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Challenge name", "example": "Summer Fitness Challenge"}, "description": {"type": "string", "description": "Challenge description", "example": "A 30-day challenge to improve your fitness and well-being"}, "startDate": {"format": "date-time", "type": "string", "description": "Challenge start date", "example": "2025-06-01T00:00:00Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Challenge end date", "example": "2025-06-30T23:59:59Z"}, "rules": {"description": "Challenge rules", "allOf": [{"$ref": "#/components/schemas/ChallengeRules"}]}, "creatorId": {"type": "string", "description": "Challenge creator user ID", "example": "123e4567-e89b-12d3-a456-************"}, "participantCount": {"type": "number", "description": "Number of participants", "example": 125}, "joined": {"type": "boolean", "description": "Whether the current user joined the challenge", "example": false}, "userProgress": {"type": "number", "description": "User progress in the challenge (0-100)", "example": 50, "nullable": true}}, "required": ["id", "name", "description", "startDate", "endDate", "rules", "creatorId", "participantCount", "joined", "userProgress"]}, "UpdateChallengeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Challenge name", "example": "Summer Fitness Challenge"}, "description": {"type": "string", "description": "Challenge description", "example": "A 30-day challenge to improve your fitness and well-being"}, "startDate": {"format": "date-time", "type": "string", "description": "Challenge start date", "example": "2025-06-01T00:00:00Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Challenge end date", "example": "2025-06-30T23:59:59Z"}, "rules": {"description": "Challenge rules", "allOf": [{"$ref": "#/components/schemas/ChallengeRulesDto"}]}}}, "ProgressDto": {"type": "object", "properties": {"progress": {"type": "number", "description": "Progress percentage (0-100)", "example": 50}}, "required": ["progress"]}, "SkillPlanMetadataDto": {"type": "object", "properties": {"category": {"type": "string", "description": "Skill plan category", "example": "Programming"}, "difficulty": {"type": "string", "description": "Skill plan difficulty level", "example": "intermediate", "enum": ["beginner", "intermediate", "advanced"]}, "estimatedDuration": {"type": "string", "description": "Estimated duration to complete the skill plan", "example": "2 weeks"}, "tags": {"description": "Skill plan tags", "example": ["programming", "typescript", "web development"], "type": "array", "items": {"type": "string"}}}, "required": ["category", "difficulty", "estimatedDuration", "tags"]}, "ResourceDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Resource type (e.g., video, article, book)", "example": "video"}, "url": {"type": "string", "description": "Resource URL", "example": "https://example.com/video123"}, "title": {"type": "string", "description": "Resource title", "example": "Introduction to TypeScript"}}, "required": ["type", "url", "title"]}, "TaskDto": {"type": "object", "properties": {"description": {"type": "string", "description": "Task description", "example": "Complete the code exercise"}, "isCompleted": {"type": "boolean", "description": "Whether the task is completed", "example": false}}, "required": ["description", "isCompleted"]}, "StepDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Step title", "example": "Getting Started with TypeScript"}, "description": {"type": "string", "description": "Step description", "example": "Learn the basics of TypeScript including types, interfaces and classes"}, "order": {"type": "number", "description": "Step order (position in sequence)", "example": 1}, "resources": {"description": "Step resources", "type": "array", "items": {"$ref": "#/components/schemas/ResourceDto"}}, "tasks": {"description": "Step tasks", "type": "array", "items": {"$ref": "#/components/schemas/TaskDto"}}}, "required": ["title", "description", "order"]}, "CreateSkillPlanDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Skill plan name", "example": "Learn TypeScript from Scratch"}, "description": {"type": "string", "description": "Skill plan description", "example": "A comprehensive guide to learning TypeScript from basics to advanced concepts"}, "isPublic": {"type": "boolean", "description": "Whether the skill plan is publicly accessible", "example": true}, "metadata": {"description": "Skill plan metadata", "allOf": [{"$ref": "#/components/schemas/SkillPlanMetadataDto"}]}, "steps": {"description": "Skill plan steps", "type": "array", "items": {"$ref": "#/components/schemas/StepDto"}}}, "required": ["name", "description", "isPublic", "metadata", "steps"]}, "Resource": {"type": "object", "properties": {"type": {"type": "string", "description": "Resource type", "example": "video"}, "url": {"type": "string", "description": "Resource URL", "example": "https://example.com/video123"}, "title": {"type": "string", "description": "Resource title", "example": "Introduction to TypeScript"}}, "required": ["type", "url", "title"]}, "Task": {"type": "object", "properties": {"description": {"type": "string", "description": "Task description", "example": "Complete the code exercise"}, "isCompleted": {"type": "boolean", "description": "Whether task is completed", "example": false}}, "required": ["description", "isCompleted"]}, "Step": {"type": "object", "properties": {"id": {"type": "number", "description": "Step ID", "example": 1}, "title": {"type": "string", "description": "Step title", "example": "Getting Started with TypeScript"}, "description": {"type": "string", "description": "Step description", "example": "Learn the basics of TypeScript including types, interfaces and classes"}, "order": {"type": "number", "description": "Step order", "example": 1}, "isCompleted": {"type": "boolean", "description": "Whether step is completed", "example": false}, "resources": {"description": "Step resources", "type": "array", "items": {"$ref": "#/components/schemas/Resource"}}, "tasks": {"description": "Step tasks", "type": "array", "items": {"$ref": "#/components/schemas/Task"}}}, "required": ["id", "title", "description", "order", "isCompleted", "resources", "tasks"]}, "SkillPlanResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique skill plan ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Skill plan name", "example": "Learn TypeScript from Scratch"}, "description": {"type": "string", "description": "Skill plan description", "example": "A comprehensive guide to learning TypeScript from basics to advanced concepts"}, "isPublic": {"type": "boolean", "description": "Whether skill plan is public", "example": true}, "creatorId": {"type": "string", "description": "Creator user ID", "example": "123e4567-e89b-12d3-a456-************"}, "createdAt": {"format": "date-time", "type": "string", "description": "Skill plan creation date", "example": "2025-05-19T10:00:00Z"}, "metadata": {"type": "object", "description": "Skill plan metadata", "example": {"category": "Programming", "difficulty": "intermediate", "estimatedDuration": "2 weeks", "tags": ["programming", "typescript", "web development"]}}, "steps": {"description": "Skill plan steps", "type": "array", "items": {"$ref": "#/components/schemas/Step"}}, "progressPercent": {"type": "number", "description": "Overall progress percentage", "example": 50}}, "required": ["id", "name", "description", "isPublic", "creatorId", "createdAt", "metadata", "steps", "progressPercent"]}, "CreateAiSkillPlanDto": {"type": "object", "properties": {"topic": {"type": "string", "description": "Topic or skill you want to learn", "example": "Learn React.js development"}, "level": {"type": "string", "description": "Your current experience level", "example": "beginner", "enum": ["beginner", "intermediate", "advanced"]}, "timeCommitment": {"type": "string", "description": "Estimated time you can dedicate per day (in minutes)", "example": 60}, "goals": {"description": "Specific learning goals or objectives", "example": "Build a portfolio website, Learn component-based development", "type": "array", "items": {"type": "string"}}, "preferredResources": {"description": "Preferred learning resources (video, article, practice, etc.)", "example": ["video", "practice"], "type": "array", "items": {"type": "string"}}, "isPublic": {"type": "boolean", "description": "Whether the skill plan should be public", "example": false}}, "required": ["topic", "level"]}, "UpdateSkillPlanDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Skill plan name", "example": "Learn TypeScript from Scratch"}, "description": {"type": "string", "description": "Skill plan description", "example": "A comprehensive guide to learning TypeScript from basics to advanced concepts"}, "isPublic": {"type": "boolean", "description": "Whether the skill plan is publicly accessible", "example": true}, "metadata": {"description": "Skill plan metadata", "allOf": [{"$ref": "#/components/schemas/SkillPlanMetadataDto"}]}, "steps": {"description": "Skill plan steps", "type": "array", "items": {"$ref": "#/components/schemas/StepDto"}}}}, "UpdateResourceDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Resource type (e.g., video, article, book)", "example": "video"}, "url": {"type": "string", "description": "Resource URL", "example": "https://example.com/updated-video"}, "title": {"type": "string", "description": "Resource title", "example": "Updated Introduction to TypeScript"}}, "required": ["type", "url", "title"]}, "UpdateStepDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Step title", "example": "Updated Getting Started with TypeScript"}, "description": {"type": "string", "description": "Step description", "example": "Updated basics of TypeScript"}, "order": {"type": "number", "description": "Step order (position in sequence)", "example": 2}, "resources": {"description": "Step resources", "type": "array", "items": {"$ref": "#/components/schemas/UpdateResourceDto"}}, "tasks": {"description": "Step tasks", "type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskDto"}}}, "required": ["title", "description", "order"]}, "UpdateStepCompletionDto": {"type": "object", "properties": {"order": {"type": "number", "description": "Step order number", "example": 1}, "completed": {"type": "boolean", "description": "Whether the step is completed", "example": true}}, "required": ["order", "completed"]}, "DailyProgress": {"type": "object", "properties": {"date": {"type": "string", "description": "Date for the progress data point", "example": "2025-05-19"}, "score": {"type": "number", "description": "Overall progress score for the day", "example": 85}, "tasksCompleted": {"type": "number", "description": "Tasks completed on this day", "example": 5}, "habitsCompleted": {"type": "number", "description": "Habits completed on this day", "example": 3}, "learningProgress": {"type": "number", "description": "Learning progress on this day (percentage)", "example": 15}}, "required": ["date", "score", "tasksCompleted", "habitsCompleted", "learningProgress"]}, "UserProgressResponseDto": {"type": "object", "properties": {"overallScore": {"type": "number", "description": "Overall progress score for the period", "example": 75}, "dailyProgress": {"description": "Daily progress breakdown", "type": "array", "items": {"$ref": "#/components/schemas/DailyProgress"}}, "totalTasksCompleted": {"type": "number", "description": "Total tasks completed in the period", "example": 28}, "totalHabitsCompleted": {"type": "number", "description": "Total habits completed in the period", "example": 21}, "changeFromPreviousPeriod": {"type": "number", "description": "Comparison to previous period (percentage change)", "example": 12.5}}, "required": ["overallScore", "dailyProgress", "totalTasksCompleted", "totalHabitsCompleted", "changeFromPreviousPeriod"]}, "HabitStreak": {"type": "object", "properties": {"name": {"type": "string", "description": "Habit name", "example": "Morning Meditation"}, "currentStreak": {"type": "number", "description": "Current streak (consecutive days)", "example": 14}, "longestStreak": {"type": "number", "description": "Longest streak ever achieved", "example": 21}, "completionRate": {"type": "number", "description": "Completion rate (percentage)", "example": 85.7}}, "required": ["name", "currentStreak", "longestStreak", "completionRate"]}, "HabitCompletionByDay": {"type": "object", "properties": {"day": {"type": "string", "description": "Day of week", "example": "Monday"}, "completionPercentage": {"type": "number", "description": "Completion percentage for this day", "example": 90}}, "required": ["day", "completionPercentage"]}, "HabitAnalyticsResponseDto": {"type": "object", "properties": {"overallCompletionRate": {"type": "number", "description": "Overall habit completion rate", "example": 82.5}, "streaks": {"description": "Habit streaks", "type": "array", "items": {"$ref": "#/components/schemas/HabitStreak"}}, "completionByDay": {"description": "Completion rates by day of week", "type": "array", "items": {"$ref": "#/components/schemas/HabitCompletionByDay"}}, "mostConsistentHabit": {"type": "string", "description": "Most consistent habit", "example": "Reading"}, "habitNeedingImprovement": {"type": "string", "description": "Habit needing most improvement", "example": "Exercise"}}, "required": ["overallCompletionRate", "streaks", "completionByDay", "mostConsistentHabit", "habitNeedingImprovement"]}, "TimeOfDayProductivity": {"type": "object", "properties": {"timeOfDay": {"type": "string", "description": "Time of day", "example": "Morning"}, "productivityScore": {"type": "number", "description": "Productivity score", "example": 85}, "tasksCompleted": {"type": "number", "description": "Number of tasks completed during this time", "example": 12}}, "required": ["timeOfDay", "productivityScore", "tasksCompleted"]}, "DayOfWeekProductivity": {"type": "object", "properties": {"dayOfWeek": {"type": "string", "description": "Day of week", "example": "Tuesday"}, "productivityScore": {"type": "number", "description": "Productivity score", "example": 90}}, "required": ["dayOfWeek", "productivityScore"]}, "ProductivityAnalyticsResponseDto": {"type": "object", "properties": {"overallProductivity": {"type": "number", "description": "Overall productivity score", "example": 78}, "productivityByTimeOfDay": {"description": "Productivity by time of day", "type": "array", "items": {"$ref": "#/components/schemas/TimeOfDayProductivity"}}, "productivityByDayOfWeek": {"description": "Productivity by day of week", "type": "array", "items": {"$ref": "#/components/schemas/DayOfWeekProductivity"}}, "mostProductiveTime": {"type": "string", "description": "Most productive time of day", "example": "Morning"}, "mostProductiveDay": {"type": "string", "description": "Most productive day of week", "example": "Tuesday"}, "averageFocusTime": {"type": "number", "description": "Focus time in minutes per day (average)", "example": 127}}, "required": ["overallProductivity", "productivityByTimeOfDay", "productivityByDayOfWeek", "mostProductiveTime", "mostProductiveDay", "averageFocusTime"]}, "DailyMood": {"type": "object", "properties": {"date": {"type": "string", "description": "Date of mood entry", "example": "2025-05-19"}, "mood": {"type": "string", "description": "Mood value", "example": "good", "enum": ["great", "good", "neutral", "bad", "terrible"]}, "moodScore": {"type": "number", "description": "Numeric representation of mood (1-5)", "example": 4}}, "required": ["date", "mood", "moodScore"]}, "MoodCorrelation": {"type": "object", "properties": {"factor": {"type": "string", "description": "Factor correlated with mood", "example": "Exercise"}, "correlationStrength": {"type": "number", "description": "Correlation strength (0-1)", "example": 0.75}, "correlationType": {"type": "string", "description": "Whether correlation is positive or negative", "example": "positive"}}, "required": ["factor", "correlationStrength", "correlationType"]}, "MoodAnalyticsResponseDto": {"type": "object", "properties": {"moodEntries": {"description": "Daily mood entries", "type": "array", "items": {"$ref": "#/components/schemas/DailyMood"}}, "averageMood": {"type": "number", "description": "Average mood score for the period", "example": 3.8}, "mostCommonMood": {"type": "string", "description": "Most common mood entry", "example": "good"}, "moodTrend": {"type": "string", "description": "Mood trend (improving, stable, declining)", "example": "improving"}, "correlations": {"description": "Factors correlated with mood", "type": "array", "items": {"$ref": "#/components/schemas/MoodCorrelation"}}}, "required": ["moodEntries", "averageMood", "mostCommonMood", "moodTrend", "correlations"]}, "Insight": {"type": "object", "properties": {"title": {"type": "string", "description": "Insight title", "example": "Morning Exercise Impact"}, "description": {"type": "string", "description": "Insight description", "example": "You are 35% more productive on days when you exercise in the morning"}, "category": {"type": "string", "description": "Insight category", "example": "Productivity"}, "confidenceLevel": {"type": "number", "description": "Confidence level (0-100)", "example": 85}}, "required": ["title", "description", "category", "confidenceLevel"]}, "Recommendation": {"type": "object", "properties": {"text": {"type": "string", "description": "Recommendation text", "example": "Try exercising for at least 15 minutes before starting your workday"}, "impactArea": {"type": "string", "description": "Expected impact area", "example": "Productivity"}, "priority": {"type": "string", "description": "Priority level", "example": "high", "enum": ["low", "medium", "high"]}}, "required": ["text", "impactArea", "priority"]}, "InsightsResponseDto": {"type": "object", "properties": {"insights": {"description": "Personalized insights", "type": "array", "items": {"$ref": "#/components/schemas/Insight"}}, "recommendations": {"description": "Personalized recommendations", "type": "array", "items": {"$ref": "#/components/schemas/Recommendation"}}, "strengths": {"description": "Areas of strength", "example": ["Morning routine", "Task completion"], "type": "array", "items": {"type": "string"}}, "improvementAreas": {"description": "Areas for improvement", "example": ["Evening productivity", "Weekend habits"], "type": "array", "items": {"type": "string"}}}, "required": ["insights", "recommendations", "strengths", "improvementAreas"]}, "FocusSessionDto": {"type": "object", "properties": {"minutes": {"type": "number", "description": "Focus session duration in minutes", "example": 45, "minimum": 1}}, "required": ["minutes"]}, "MoodEntryDto": {"type": "object", "properties": {"mood": {"type": "string", "description": "Current mood", "example": "good", "enum": ["great", "good", "neutral", "bad", "terrible"]}}, "required": ["mood"]}, "DailyStats": {"type": "object", "properties": {"day": {"type": "string", "description": "Day of week", "example": "Monday"}, "tasksCompleted": {"type": "number", "description": "Tasks completed", "example": 8}, "habitsCompleted": {"type": "number", "description": "Habits completed", "example": 5}, "focusMinutes": {"type": "number", "description": "Focus minutes", "example": 125}, "productivityScore": {"type": "number", "description": "Productivity score", "example": 85}}, "required": ["day", "tasksCompleted", "habitsCompleted", "focusMinutes", "productivityScore"]}, "WeeklyStatsResponseDto": {"type": "object", "properties": {"dailyStats": {"description": "Weekly stats by day", "type": "array", "items": {"$ref": "#/components/schemas/DailyStats"}}, "totalTasks": {"type": "number", "description": "Total tasks completed this week", "example": 42}, "totalHabits": {"type": "number", "description": "Total habits completed this week", "example": 28}, "totalFocusMinutes": {"type": "number", "description": "Total focus minutes this week", "example": 645}, "weeklyProductivityScore": {"type": "number", "description": "Weekly productivity score", "example": 78}, "mostProductiveDay": {"type": "string", "description": "Most productive day", "example": "Tuesday"}}, "required": ["dailyStats", "totalTasks", "totalHabits", "totalFocusMinutes", "weeklyProductivityScore", "mostProductiveDay"]}, "TaskCompletionByDay": {"type": "object", "properties": {"day": {"type": "string", "description": "Day", "example": "2025-05-19"}, "completed": {"type": "number", "description": "Tasks completed", "example": 6}, "due": {"type": "number", "description": "Tasks due", "example": 8}, "completionRate": {"type": "number", "description": "Completion rate", "example": 75}}, "required": ["day", "completed", "due", "completionRate"]}, "TaskCompletionByPriority": {"type": "object", "properties": {"priority": {"type": "string", "description": "Priority level", "example": "high"}, "completed": {"type": "number", "description": "Tasks completed", "example": 15}, "due": {"type": "number", "description": "Tasks due", "example": 18}, "completionRate": {"type": "number", "description": "Completion rate", "example": 83.3}}, "required": ["priority", "completed", "due", "completionRate"]}, "TaskCompletionStatsResponseDto": {"type": "object", "properties": {"overallCompletionRate": {"type": "number", "description": "Overall task completion rate", "example": 78.5}, "onTimeCompletionRate": {"type": "number", "description": "Tasks completed on time rate", "example": 65.2}, "completionByDay": {"description": "Task completion by day", "type": "array", "items": {"$ref": "#/components/schemas/TaskCompletionByDay"}}, "completionByPriority": {"description": "Task completion by priority", "type": "array", "items": {"$ref": "#/components/schemas/TaskCompletionByPriority"}}, "averageTasksPerDay": {"type": "number", "description": "Average tasks completed per day", "example": 4.3}, "mostProductiveDay": {"type": "string", "description": "Most productive day for completing tasks", "example": "Monday"}}, "required": ["overallCompletionRate", "onTimeCompletionRate", "completionByDay", "completionByPriority", "averageTasksPerDay", "mostProductiveDay"]}, "UpdateDailyProgressDto": {"type": "object", "properties": {"xpGained": {"type": "number", "description": "XP points gained", "example": 10, "minimum": 0}, "badgeEarned": {"type": "string", "description": "Badge earned", "example": "early_bird"}, "focusMinutes": {"type": "number", "description": "Focus session duration in minutes", "example": 25, "minimum": 0}, "podcastListened": {"type": "boolean", "description": "Whether a podcast was listened to", "example": true}}}, "SkillPlansUpdateDto": {"type": "object", "properties": {"active": {"type": "number", "description": "Number of active skill plans", "example": 2, "minimum": 0}, "completedSteps": {"type": "number", "description": "Number of completed steps", "example": 15, "minimum": 0}, "totalSteps": {"type": "number", "description": "Total number of steps", "example": 20, "minimum": 0}}, "required": ["active", "completedSteps", "totalSteps"]}, "UpdateUserProgressDto": {"type": "object", "properties": {"skillPlans": {"description": "Skill plans progress update", "allOf": [{"$ref": "#/components/schemas/SkillPlansUpdateDto"}]}, "xpGained": {"type": "number", "description": "XP points gained", "example": 50, "minimum": 0}, "badgesEarned": {"description": "Array of badges earned", "example": ["consistency_master", "focus_champion"], "type": "array", "items": {"type": "string"}}}}, "LifestyleMetric": {"type": "object", "properties": {"name": {"type": "string", "description": "Metric name", "example": "Task Completion Rate"}, "currentScore": {"type": "number", "description": "Current score (0-100)", "example": 75}, "targetScore": {"type": "number", "description": "Target score (0-100)", "example": 85}, "trend": {"type": "string", "description": "Trend direction", "example": "improving", "enum": ["improving", "declining", "stable"]}, "changePercentage": {"type": "number", "description": "Percentage change from last period", "example": 12.5}}, "required": ["name", "currentScore", "targetScore", "trend", "changePercentage"]}, "ImprovementAction": {"type": "object", "properties": {"title": {"type": "string", "description": "Action title", "example": "Establish Morning Routine"}, "description": {"type": "string", "description": "Detailed description of the action", "example": "Start your day with a 10-minute meditation followed by reviewing your top 3 priorities"}, "impact": {"type": "string", "description": "Expected impact level", "example": "high", "enum": ["low", "medium", "high"]}, "difficulty": {"type": "string", "description": "Implementation difficulty", "example": "medium", "enum": ["easy", "medium", "hard"]}, "timeToResults": {"type": "number", "description": "Estimated time to see results (in days)", "example": 14}, "category": {"type": "string", "description": "Category of improvement", "example": "Productivity"}}, "required": ["title", "description", "impact", "difficulty", "timeToResults", "category"]}, "LifestyleImprovement": {"type": "object", "properties": {"area": {"type": "string", "description": "Area of improvement", "example": "Morning Productivity"}, "currentScore": {"type": "number", "description": "Current performance score (0-100)", "example": 68}, "improvementPotential": {"type": "number", "description": "Potential improvement percentage", "example": 25}, "actions": {"description": "Specific actions to take", "type": "array", "items": {"$ref": "#/components/schemas/ImprovementAction"}}, "evidence": {"type": "string", "description": "Evidence supporting this improvement area", "example": "Your task completion rate drops by 40% in the afternoon compared to morning"}}, "required": ["area", "currentScore", "improvementPotential", "actions", "evidence"]}, "PersonalityInsight": {"type": "object", "properties": {"insight": {"type": "string", "description": "Insight about user behavior pattern", "example": "You are a morning person who performs best between 8-11 AM"}, "category": {"type": "string", "description": "Behavioral pattern category", "example": "Circadian Rhythm"}, "confidence": {"type": "number", "description": "Confidence level of this insight (0-100)", "example": 87}}, "required": ["insight", "category", "confidence"]}, "AIImprovementReportResponseDto": {"type": "object", "properties": {"overallScore": {"type": "number", "description": "Overall lifestyle health score (0-100)", "example": 72}, "metrics": {"description": "Key lifestyle metrics with scores and trends", "type": "array", "items": {"$ref": "#/components/schemas/LifestyleMetric"}}, "improvements": {"description": "Top improvement opportunities ranked by impact", "type": "array", "items": {"$ref": "#/components/schemas/LifestyleImprovement"}}, "personalityInsights": {"description": "Insights about user behavior patterns", "type": "array", "items": {"$ref": "#/components/schemas/PersonalityInsight"}}, "strengths": {"description": "Areas where user is performing well", "example": ["Habit consistency", "Goal completion", "Sleep schedule"], "type": "array", "items": {"type": "string"}}, "criticalAreas": {"description": "Critical areas needing immediate attention", "example": ["Afternoon productivity slump", "Weekend routine inconsistency"], "type": "array", "items": {"type": "string"}}, "projectedScore": {"type": "number", "description": "Projected lifestyle score after implementing top 3 improvements", "example": 84}, "analysisDateRange": {"type": "number", "description": "Number of days of data used for this analysis", "example": 30}, "generatedAt": {"format": "date-time", "type": "string", "description": "When this report was generated", "example": "2025-06-13T10:30:00Z"}, "motivationalMessage": {"type": "string", "description": "Motivational message based on progress", "example": "Great progress this month! Your consistency has improved by 15%. Keep up the momentum!"}}, "required": ["overallScore", "metrics", "improvements", "personalityInsights", "strengths", "criticalAreas", "projectedScore", "analysisDateRange", "generatedAt", "motivationalMessage"]}, "PodcastResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique podcast ID", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Podcast title", "example": "Your Daily Mindfulness Coach"}, "description": {"type": "string", "description": "Podcast description", "example": "A personalized meditation session focusing on stress reduction"}, "audioUrl": {"type": "string", "description": "Podcast audio URL", "example": "https://storage.example.com/podcasts/123e4567-e89b-12d3-a456-************.mp3"}, "durationSeconds": {"type": "number", "description": "Podcast duration in seconds", "example": 600}, "createdAt": {"format": "date-time", "type": "string", "description": "Podcast generation date", "example": "2025-05-19T10:00:00Z"}, "listened": {"type": "boolean", "description": "Whether the podcast has been listened to", "example": false}, "userId": {"type": "string", "description": "User ID who owns the podcast", "example": "123e4567-e89b-12d3-a456-************"}}, "required": ["id", "title", "description", "audioUrl", "durationSeconds", "createdAt", "listened", "userId"]}, "CustomPodcastPreferencesDto": {"type": "object", "properties": {"topic": {"type": "string", "description": "Optional podcast topic preference", "example": "Morning Motivation"}, "mood": {"type": "string", "description": "Optional mood preference to influence podcast tone", "example": "energetic"}, "language": {"type": "string", "description": "Optional language preference for podcast content", "example": "English"}}}, "CreateSubscriptionDto": {"type": "object", "properties": {"planId": {"type": "string"}, "couponId": {"type": "string"}, "originalTransactionId": {"type": "string"}, "latestReceipt": {"type": "string"}, "purchaseToken": {"type": "string"}, "startsAt": {"type": "string"}, "expiresAt": {"type": "string"}, "isFreeTrialTier": {"type": "boolean"}, "freeTrialEndsAt": {"type": "string"}, "price": {"type": "number"}, "currency": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["planId", "startsAt", "expiresAt"]}, "UpdateSubscriptionDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["active", "expired", "cancelled", "pending", "grace_period", "free_trial"]}, "latestReceipt": {"type": "string"}, "purchaseToken": {"type": "string"}, "expiresAt": {"type": "string"}, "cancelledAt": {"type": "string"}, "autoRenew": {"type": "boolean"}, "metadata": {"type": "object"}}}, "VerifyReceiptDto": {"type": "object", "properties": {"receipt": {"type": "string"}, "productId": {"type": "string"}}, "required": ["receipt"]}, "VerifyPurchaseTokenDto": {"type": "object", "properties": {"purchaseToken": {"type": "string"}, "productId": {"type": "string"}, "packageName": {"type": "string"}}, "required": ["purchaseToken", "productId", "packageName"]}, "ValidateCouponDto": {"type": "object", "properties": {"code": {"type": "string"}, "planId": {"type": "string"}}, "required": ["code"]}, "CreateSubscriptionPlanDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["weekly", "monthly", "yearly"]}, "platform": {"type": "string", "enum": ["ios", "android", "web"]}, "price": {"type": "number"}, "currency": {"type": "string", "default": "USD"}, "productId": {"type": "string"}, "hasFreeTrialTier": {"type": "boolean", "default": false}, "freeTrialDays": {"type": "number", "default": 0}, "active": {"type": "boolean", "default": true}, "features": {"type": "array", "items": {"type": "string"}}, "sortOrder": {"type": "number", "default": 0}}, "required": ["name", "type", "platform", "price", "currency", "productId", "hasFreeTrialTier", "freeTrialDays", "active", "sortOrder"]}, "UpdateSubscriptionPlanDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "price": {"type": "number"}, "currency": {"type": "string"}, "productId": {"type": "string"}, "hasFreeTrialTier": {"type": "boolean"}, "freeTrialDays": {"type": "number"}, "active": {"type": "boolean"}, "features": {"type": "array", "items": {"type": "string"}}, "sortOrder": {"type": "number"}}}, "CreateCouponCodeDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["percentage", "fixed_amount", "free_trial_extension"]}, "value": {"type": "number"}, "maxUses": {"type": "number"}, "validFrom": {"type": "string"}, "validUntil": {"type": "string"}, "firstTimeOnly": {"type": "boolean", "default": false}, "applicablePlans": {"type": "array", "items": {"type": "string"}}, "active": {"type": "boolean", "default": true}, "minimumPurchaseAmount": {"type": "number"}, "freeTrialDays": {"type": "number"}}, "required": ["code", "name", "type", "validFrom", "validUntil", "firstTimeOnly", "active"]}, "UpdateCouponCodeDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "value": {"type": "number"}, "maxUses": {"type": "number"}, "validFrom": {"type": "string"}, "validUntil": {"type": "string"}, "firstTimeOnly": {"type": "boolean"}, "applicablePlans": {"type": "array", "items": {"type": "string"}}, "active": {"type": "boolean"}, "minimumPurchaseAmount": {"type": "number"}, "freeTrialDays": {"type": "number"}}}, "FirebaseMetadataDto": {"type": "object", "properties": {"createdAt": {"type": "string", "description": "Created at timestamp", "example": "2023-05-20T12:00:00.000Z"}}}, "FirebaseEventDataDto": {"type": "object", "properties": {"uid": {"type": "string", "description": "Firebase user ID", "example": "firebase-uid-123456"}, "email": {"type": "string", "description": "User email", "example": "<EMAIL>"}, "metadata": {"description": "User metadata", "allOf": [{"$ref": "#/components/schemas/FirebaseMetadataDto"}]}}, "required": ["uid"]}, "FirebaseEventDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Event type", "example": "user.created", "enum": ["user.created", "user.deleted"]}, "data": {"description": "Event data", "allOf": [{"$ref": "#/components/schemas/FirebaseEventDataDto"}]}}, "required": ["type", "data"]}, "FirebaseWebhookDto": {"type": "object", "properties": {"event": {"description": "Firebase event", "allOf": [{"$ref": "#/components/schemas/FirebaseEventDto"}]}}, "required": ["event"]}, "ContactUsDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Full name of the person contacting", "example": "<PERSON>"}, "email": {"type": "string", "description": "Email address for response", "example": "<EMAIL>"}, "subject": {"type": "string", "description": "Subject of the inquiry", "example": "Question about premium features"}, "message": {"type": "string", "description": "Message content", "example": "I would like to know more about the premium features available in the app."}, "userId": {"type": "string", "description": "User ID if logged in", "example": "123e4567-e89b-12d3-a456-************"}, "category": {"type": "string", "description": "Category of the inquiry", "example": "technical", "enum": ["technical", "billing", "feature-request", "bug-report", "general"]}}, "required": ["name", "email", "subject", "message"]}, "FeedbackDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of feedback", "enum": ["bug", "feature-request", "improvement", "compliment", "complaint"], "example": "feature-request"}, "title": {"type": "string", "description": "Title of the feedback", "example": "Add dark mode support"}, "description": {"type": "string", "description": "Detailed description of the feedback", "example": "It would be great to have a dark mode option for better usability during night time."}, "priority": {"type": "string", "description": "Priority level of the feedback", "enum": ["low", "medium", "high", "critical"], "example": "medium"}, "email": {"type": "string", "description": "User email for follow-up", "example": "<EMAIL>"}, "userId": {"type": "string", "description": "User ID if authenticated", "example": "123e4567-e89b-12d3-a456-************"}, "deviceInfo": {"type": "string", "description": "Device information for bug reports", "example": "iOS 17.0, iPhone 14 Pro"}, "appVersion": {"type": "string", "description": "App version when feedback was submitted", "example": "1.2.3"}}, "required": ["type", "title", "description"]}, "HelpArticleResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Article ID"}, "title": {"type": "string", "description": "Article title"}, "description": {"type": "string", "description": "Article description"}, "content": {"type": "string", "description": "Article content"}, "category": {"type": "string", "description": "Article category", "enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"]}, "tags": {"description": "Article tags", "type": "array", "items": {"type": "string"}}, "published": {"type": "boolean", "description": "Whether the article is published"}, "sortOrder": {"type": "number", "description": "Display order"}, "viewCount": {"type": "number", "description": "View count"}, "helpfulCount": {"type": "number", "description": "Helpful count"}, "notHelpfulCount": {"type": "number", "description": "Not helpful count"}, "authorId": {"type": "string", "description": "Author ID"}, "createdAt": {"format": "date-time", "type": "string", "description": "Created date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Updated date"}}, "required": ["id", "title", "description", "content", "category", "tags", "published", "sortOrder", "viewCount", "helpfulCount", "notHelpfulCount", "authorId", "createdAt", "updatedAt"]}, "CreateHelpArticleDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Article title", "example": "Getting Started with Power Up"}, "description": {"type": "string", "description": "Article description", "example": "Learn how to get started with the app"}, "content": {"type": "string", "description": "Article content in HTML format"}, "category": {"type": "string", "description": "Article category", "enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"]}, "tags": {"description": "Article tags", "type": "array", "items": {"type": "string"}}, "published": {"type": "boolean", "description": "Whether the article is published", "default": true}, "sortOrder": {"type": "number", "description": "Display order (lower numbers appear first)", "default": 0}}, "required": ["title", "description", "content", "category"]}, "UpdateHelpArticleDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Article title"}, "description": {"type": "string", "description": "Article description"}, "content": {"type": "string", "description": "Article content in HTML format"}, "category": {"type": "string", "description": "Article category", "enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"]}, "tags": {"description": "Article tags", "type": "array", "items": {"type": "string"}}, "published": {"type": "boolean", "description": "Whether the article is published"}, "sortOrder": {"type": "number", "description": "Display order (lower numbers appear first)"}}}, "CreateBlogPostDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Blog post title", "example": "The Future of AI in Personal Wellness"}, "excerpt": {"type": "string", "description": "Short excerpt/summary", "example": "Discover how AI is revolutionizing personal wellness and habit formation."}, "content": {"type": "string", "description": "Blog post content in markdown", "example": "# Introduction\n\nThis is the content..."}, "slug": {"type": "string", "description": "URL slug (auto-generated if not provided)", "example": "future-ai-personal-wellness"}, "category": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "description": "Blog post category", "example": "ai-technology"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "description": "Blog post status", "example": "draft"}, "featuredImage": {"type": "string", "description": "Featured image URL", "example": "https://example.com/image.jpg"}, "metaDescription": {"type": "string", "description": "Meta description for SEO", "example": "Learn about AI wellness technology trends"}, "tags": {"description": "Tags array", "example": ["AI", "wellness", "technology"], "type": "array", "items": {"type": "string"}}, "isFeatured": {"type": "boolean", "description": "<PERSON> as featured post", "example": false}}, "required": ["title", "content", "category"]}, "UpdateBlogPostDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Blog post title", "example": "The Future of AI in Personal Wellness"}, "excerpt": {"type": "string", "description": "Short excerpt/summary", "example": "Discover how AI is revolutionizing personal wellness and habit formation."}, "content": {"type": "string", "description": "Blog post content in markdown", "example": "# Introduction\n\nThis is the content..."}, "slug": {"type": "string", "description": "URL slug (auto-generated if not provided)", "example": "future-ai-personal-wellness"}, "category": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "description": "Blog post category", "example": "ai-technology"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "description": "Blog post status", "example": "draft"}, "featuredImage": {"type": "string", "description": "Featured image URL", "example": "https://example.com/image.jpg"}, "metaDescription": {"type": "string", "description": "Meta description for SEO", "example": "Learn about AI wellness technology trends"}, "tags": {"description": "Tags array", "example": ["AI", "wellness", "technology"], "type": "array", "items": {"type": "string"}}, "isFeatured": {"type": "boolean", "description": "<PERSON> as featured post", "example": false}}}, "BlogPostResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Blog post ID"}, "title": {"type": "string", "description": "Blog post title"}, "excerpt": {"type": "string", "description": "Short excerpt"}, "content": {"type": "string", "description": "Blog post content"}, "slug": {"type": "string", "description": "URL slug"}, "category": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "description": "Blog post category"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "description": "Blog post status"}, "featuredImage": {"type": "string", "description": "Featured image URL"}, "metaDescription": {"type": "string", "description": "Meta description"}, "tags": {"description": "Tags array", "type": "array", "items": {"type": "string"}}, "views": {"type": "number", "description": "View count"}, "likes": {"type": "number", "description": "Like count"}, "isFeatured": {"type": "boolean", "description": "Is featured post"}, "publishedAt": {"format": "date-time", "type": "string", "description": "Published date"}, "author": {"type": "object", "description": "Author information"}, "createdAt": {"format": "date-time", "type": "string", "description": "Created date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Updated date"}}, "required": ["id", "title", "excerpt", "content", "slug", "category", "status", "featuredImage", "metaDescription", "tags", "views", "likes", "isFeatured", "publishedAt", "author", "createdAt", "updatedAt"]}, "CreateCalendarEventDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the calendar event", "example": "Team Meeting"}, "description": {"type": "string", "description": "Description of the calendar event", "example": "Weekly team standup meeting"}, "startTime": {"type": "string", "description": "Start time of the event", "example": "2025-06-28T10:00:00Z"}, "endTime": {"type": "string", "description": "End time of the event", "example": "2025-06-28T11:00:00Z"}, "type": {"type": "string", "description": "Type of the calendar event", "enum": ["habit", "task", "custom"], "default": "custom"}, "relatedId": {"type": "string", "description": "ID of the related task or habit", "example": "uuid-string"}, "color": {"type": "string", "description": "Hex color code for the event", "example": "#FF5722"}, "isCompleted": {"type": "boolean", "description": "Whether the event is completed", "default": false}}, "required": ["title", "startTime", "endTime", "type"]}, "CalendarEventResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the calendar event"}, "title": {"type": "string", "description": "Title of the calendar event"}, "description": {"type": "string", "description": "Description of the calendar event"}, "startTime": {"format": "date-time", "type": "string", "description": "Start time of the event"}, "endTime": {"format": "date-time", "type": "string", "description": "End time of the event"}, "type": {"type": "string", "description": "Type of the calendar event", "enum": ["habit", "task", "custom"]}, "relatedId": {"type": "string", "description": "ID of the related task or habit"}, "color": {"type": "string", "description": "Hex color code for the event"}, "isCompleted": {"type": "boolean", "description": "Whether the event is completed"}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp"}}, "required": ["id", "title", "startTime", "endTime", "type", "isCompleted", "createdAt", "updatedAt"]}, "UpdateCalendarEventDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the calendar event", "example": "Team Meeting"}, "description": {"type": "string", "description": "Description of the calendar event", "example": "Weekly team standup meeting"}, "startTime": {"type": "string", "description": "Start time of the event", "example": "2025-06-28T10:00:00Z"}, "endTime": {"type": "string", "description": "End time of the event", "example": "2025-06-28T11:00:00Z"}, "type": {"type": "string", "description": "Type of the calendar event", "enum": ["habit", "task", "custom"], "default": "custom"}, "relatedId": {"type": "string", "description": "ID of the related task or habit", "example": "uuid-string"}, "color": {"type": "string", "description": "Hex color code for the event", "example": "#FF5722"}, "isCompleted": {"type": "boolean", "description": "Whether the event is completed", "default": false}}}, "CreateMobileFeedbackDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of feedback", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"], "example": "feature-request"}, "title": {"type": "string", "description": "Title/summary of the feedback", "example": "Add dark mode support", "maxLength": 200}, "description": {"type": "string", "description": "Detailed description of the feedback", "example": "It would be great to have a dark mode option for better usability during night time."}, "priority": {"type": "string", "description": "Priority level of the feedback", "enum": ["low", "medium", "high", "critical"], "example": "medium"}, "platform": {"type": "string", "description": "Mobile platform", "enum": ["ios", "android", "web"], "example": "ios"}, "appVersion": {"type": "string", "description": "App version", "example": "1.2.3"}, "deviceModel": {"type": "string", "description": "Device model", "example": "iPhone 14 Pro"}, "osVersion": {"type": "string", "description": "Operating system version", "example": "iOS 17.0"}, "screenResolution": {"type": "string", "description": "Screen resolution", "example": "1179x2556"}, "email": {"type": "string", "description": "User email for follow-up", "example": "<EMAIL>"}, "userName": {"type": "string", "description": "User name", "example": "<PERSON>"}, "contactBack": {"type": "boolean", "description": "Whether user wants to be contacted back", "example": true}, "anonymous": {"type": "boolean", "description": "Whether feedback should be anonymous", "example": false}, "rating": {"type": "number", "description": "Overall rating (1-5 stars)", "example": 4, "minimum": 1, "maximum": 5}, "featureContext": {"type": "string", "description": "Feature or screen context", "example": "Habits tracking screen"}, "reproductionSteps": {"type": "string", "description": "Steps to reproduce the issue (for bug reports)", "example": "1. Open habits screen\n2. Tap on add habit\n3. App crashes"}, "expectedBehavior": {"type": "string", "description": "Expected behavior (for bug reports)", "example": "App should open the add habit form"}, "actualBehavior": {"type": "string", "description": "Actual behavior (for bug reports)", "example": "App crashes and shows error message"}, "screenshotUrls": {"description": "Screenshot URLs", "example": ["https://example.com/screenshot1.jpg", "https://example.com/screenshot2.jpg"], "type": "array", "items": {"type": "string"}}, "videoUrl": {"type": "string", "description": "Video URL for demonstration", "example": "https://example.com/video.mp4"}, "tags": {"description": "Tags for categorization", "example": ["urgent", "accessibility", "mobile"], "type": "array", "items": {"type": "string"}}}, "required": ["type", "title", "description"]}, "MobileFeedbackResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Feedback ID", "example": "123e4567-e89b-12d3-a456-************"}, "type": {"type": "string", "description": "Type of feedback", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}, "title": {"type": "string", "description": "Feedback title"}, "description": {"type": "string", "description": "Feedback description"}, "priority": {"type": "string", "description": "Priority level", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "description": "Current status", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}, "platform": {"type": "string", "description": "Mobile platform", "enum": ["ios", "android", "web"]}, "appVersion": {"type": "string", "description": "App version"}, "deviceModel": {"type": "string", "description": "Device model"}, "osVersion": {"type": "string", "description": "OS version"}, "userId": {"type": "string", "description": "User ID"}, "email": {"type": "string", "description": "User email"}, "userName": {"type": "string", "description": "User name"}, "contactBack": {"type": "boolean", "description": "Contact back preference"}, "anonymous": {"type": "boolean", "description": "Anonymous feedback"}, "rating": {"type": "number", "description": "Rating (1-5 stars)"}, "featureContext": {"type": "string", "description": "Feature context"}, "screenshotUrls": {"description": "Screenshot URLs", "type": "array", "items": {"type": "string"}}, "videoUrl": {"type": "string", "description": "Video URL"}, "adminNotes": {"type": "string", "description": "Admin notes"}, "assignedTo": {"type": "string", "description": "Assigned admin"}, "resolutionNotes": {"type": "string", "description": "Resolution notes"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update date"}, "resolvedAt": {"format": "date-time", "type": "string", "description": "Resolution date"}, "dueDate": {"format": "date-time", "type": "string", "description": "Due date"}}, "required": ["id", "type", "title", "description", "priority", "status", "contactBack", "anonymous", "createdAt", "updatedAt"]}, "UpdateMobileFeedbackDto": {"type": "object", "properties": {"status": {"type": "string", "description": "Feedback status", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}, "priority": {"type": "string", "description": "Priority level", "enum": ["low", "medium", "high", "critical"]}, "adminNotes": {"type": "string", "description": "Admin notes"}, "assignedTo": {"type": "string", "description": "Assigned admin user ID"}, "resolutionNotes": {"type": "string", "description": "Resolution notes"}, "estimatedHours": {"type": "number", "description": "Estimated hours for completion"}, "actualHours": {"type": "number", "description": "Actual hours spent"}, "dueDate": {"type": "string", "description": "Due date for resolution"}, "resolvedAt": {"type": "string", "description": "Resolution date"}}}}}}