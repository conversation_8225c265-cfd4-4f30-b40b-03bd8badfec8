/// Routes constants for the application
class AppRoutes {
  /// Private constructor to prevent instantiation
  AppRoutes._();

  // Main routes
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';

  // Dashboard routes
  static const String dashboard = '/dashboard';
  static const String habits = '/habits';
  static const String tasks = '/tasks';
  static const String calendar = '/calendar';
  static const String community = '/community';
  static const String podcasts = '/podcasts';
  static const String settings = '/settings';

  // Feature specific routes
  static const String habitDetail = '/habit-detail';
  static const String taskDetail = '/task-detail';
  static const String podcastPlayer = '/podcast-player';
  static const String profile = '/profile';
  static const String profileEdit = '/profile/edit';
  static const String notifications = '/notifications';
  static const String statistics = '/statistics';
  static const String coachingSuggestions = '/coaching-suggestions';
  static const String progressReport = '/progress-report';
  static const String achievements = '/achievements';

  // Skill Plans routes
  static const String skillPlans = '/skill-plans';
  static const String skillPlanDetail = '/skill-plans/details/:id';
  static const String createCustomPlan = '/skill-plans/create';

  // Settings routes
  static const String feedback = '/feedback';
  static const String help = '/help';
  static const String helpArticleDetail = '/help/article';
  static const String legal = '/legal';
}
