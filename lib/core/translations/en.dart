/// English translations
const Map<String, String> en = {
  // General
  'app_name': 'Power Up',
  'welcome': 'Welcome',
  'welcome_user': 'Welcome, @name!',
  'success': 'Success',
  'error': 'Error',
  'loading': 'Loading...',
  'retry': 'Retry',
  'cancel': 'Cancel',
  'continue': 'Continue',
  'done': 'Done',
  'save': 'Save',
  'delete': 'Delete',
  'edit': 'Edit',
  'confirm': 'Confirm',
  'yes': 'Yes',
  'no': 'No',
  'ok': 'OK',
  'back': 'Back',
  'close': 'Close',
  'search': 'Search',
  'filter': 'Filter',
  'sort': 'Sort',
  'settings': 'Settings',
  'help': 'Help',
  'about': 'About',
  'version': 'Version',
  'update': 'Update',
  'refresh': 'Refresh',
  'home': 'Home',
  'profile': 'Profile',

  // Navigation
  'analytics': 'Analytics',
  'habits': 'Habits',
  'tasks': 'Tasks',
  'calendar': 'Calendar',
  'skill_plans': 'Skill Plans',
  'community': 'Community',
  'podcasts': 'Podcasts',

  // Authentication
  'login': 'Login',
  'logout': 'Logout',
  'register': 'Register',
  'email': 'Email',
  'password': 'Password',
  'confirm_password': 'Confirm Password',
  'forgot_password': 'Forgot Password?',
  'reset_password': 'Reset Password',
  'remember_me': 'Remember me',
  'sign_in_with_google': 'Sign in with Google',
  'sign_in_with_apple': 'Sign in with Apple',
  'create_account': 'Create Account',
  'already_have_account': 'Already have an account?',
  'dont_have_account': "Don't have an account?",
  'guest_user': 'Guest User',
  'tap_to_login': 'Tap to login',

  // Settings
  'preferences': 'Preferences',
  'notifications': 'Notifications',
  'manage_notifications': 'Manage your notifications',
  'theme': 'Theme',
  'choose_theme': 'Choose your preferred theme',
  'language': 'Language',
  'choose_language': 'Choose your preferred language',
  'english': 'English',
  'arabic': 'العربية',
  'privacy': 'Privacy',
  'privacy_data_section': 'Privacy & Data',
  'terms_of_service': 'Terms of Service',
  'privacy_policy': 'Privacy Policy',
  'about_us': 'About Us',
  'feedback': 'Feedback',
  'rate_app': 'Rate App',
  'contact_us': 'Contact Us',

  // Help
  'help_faq': 'Help & FAQ',
  'frequently_asked_questions': 'Frequently Asked Questions',
  'search_help': 'Search for help...',
  'no_results_found': 'No results found',
  'article_not_found': 'Article not found',
  'loading_help': 'Loading help articles...',

  // Messages
  'language_updated': 'Language updated successfully',
  'theme_updated': 'Theme updated successfully',
  'settings_saved': 'Settings saved successfully',
  'feedback_submitted': 'Feedback submitted successfully',
  'error_occurred': 'An error occurred',
  'network_error': 'Network error. Please check your connection.',
  'server_error': 'Server error. Please try again later.',
  'auth_error': 'Authentication error. Please login again.',
  'no_internet': 'No internet connection',
  'timeout_error': 'Request timeout. Please try again.',

  // Notifications
  'notification_settings': 'Notification Settings',
  'push_notifications': 'Push Notifications',
  'email_notifications': 'Email Notifications',
  'in_app_notifications': 'In-App Notifications',
  'sound': 'Sound',
  'vibration': 'Vibration',
  'badge': 'Badge',

  // Legal
  'legal_documents': 'Legal Documents',
  'terms_and_conditions': 'Terms and Conditions',
  'last_updated': 'Last updated: @date',
  'contact_info': 'Contact Information',
  'document_loading': 'Loading document...',
  'document_error': 'Error loading document',

  // Common actions
  'view_details': 'View Details',
  'read_more': 'Read More',
  'show_less': 'Show Less',
  'expand': 'Expand',
  'collapse': 'Collapse',
  'share': 'Share',
  'copy': 'Copy',
  'download': 'Download',
  'upload': 'Upload',
  'select_all': 'Select All',
  'clear_all': 'Clear All',

  // RTL/LTR Drawer Example
  'drawer_rtl_example': 'RTL/LTR Drawer Example',
  'toggle_language': 'Toggle Language',
  'rtl_ltr_support': 'RTL/LTR Support',
  'drawer_rtl_description':
      'The custom 3D drawer automatically adapts to both left-to-right (LTR) and right-to-left (RTL) layouts, providing a seamless experience in any language.',
  'features': 'Features',
  'rtl_layout_support': 'Automatic RTL layout detection',
  'dynamic_animations': 'Dynamic slide animations',
  'mirrored_shadows': 'Mirrored shadow effects',
  'proper_text_alignment': 'Proper text alignment',
  'responsive_icons': 'Responsive icon directions',
  'how_it_works': 'How It Works',
  'drawer_mechanism':
      'The drawer uses Directionality.of(context) to detect the current text direction and automatically adjusts positioning, animations, shadows, and text alignment accordingly.',
};
