import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_local_data_source.dart';
import 'package:power_up/features/ai_reports/data/models/progress_report_model.dart';
import 'package:power_up/features/ai_reports/data/models/coaching_suggestion_model.dart';
import 'package:power_up/features/ai_reports/data/models/habit_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/productivity_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/mood_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/personalized_insights_model.dart';
import 'package:power_up/features/ai_reports/data/models/weekly_stats_model.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';

/// Implementation of AiReportLocalDataSource using StorageService
class AiReportLocalDataSourceImpl implements AiReportLocalDataSource {
  // Storage keys
  static const String _weeklyProgressReportKey = 'ai_weekly_progress_report';
  static const String _coachingSuggestionsKey = 'ai_coaching_suggestions';
  static const String _userProgressKey = 'ai_user_progress';
  static const String _habitAnalyticsKey = 'ai_habit_analytics';
  static const String _productivityAnalyticsKey = 'ai_productivity_analytics';
  static const String _moodAnalyticsKey = 'ai_mood_analytics';
  static const String _personalizedInsightsKey = 'ai_personalized_insights';
  static const String _weeklyStatsKey = 'ai_weekly_stats';
  static const String _habitCorrelationsKey = 'ai_habit_correlations';

  late final StorageService _storageService;
  bool _isInitialized = false;

  AiReportLocalDataSourceImpl({StorageService? storageService}) {
    if (storageService != null) {
      _storageService = storageService;
      _isInitialized = true;
    }
  }

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      _storageService = await StorageService.getInstance();
      _isInitialized = true;
    }
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose for StorageService
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }

  @override
  Future<T?> get<T>({
    required String key,
    required T Function(dynamic json) fromJson,
  }) async {
    await _ensureInitialized();
    final data = _storageService.getData(key);
    return data != null ? fromJson(data) : null;
  }

  @override
  Future<void> set<T>({required String key, required T data}) async {
    await _ensureInitialized();
    await _storageService.setData(key, data);
  }

  @override
  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _storageService.removeData(key);
  }

  @override
  Future<void> clear() async {
    await _ensureInitialized();
    await clearAllCache();
  }

  // Weekly Progress Report
  @override
  Future<void> cacheWeeklyProgressReport(ProgressReportModel report) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(_weeklyProgressReportKey, report.toJson());
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache weekly progress report: $e',
      );
    }
  }

  @override
  Future<ProgressReportModel?> getCachedWeeklyProgressReport() async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<Map<String, dynamic>>(
        _weeklyProgressReportKey,
      );
      return data != null ? ProgressReportModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached weekly progress report: $e',
      );
    }
  }

  // Coaching Suggestions
  @override
  Future<void> cacheCoachingSuggestions(
    List<CoachingSuggestionModel> suggestions,
  ) async {
    try {
      await _ensureInitialized();
      final suggestionsJson = suggestions.map((s) => s.toJson()).toList();
      await _storageService.setData(_coachingSuggestionsKey, suggestionsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to cache coaching suggestions: $e');
    }
  }

  @override
  Future<List<CoachingSuggestionModel>> getCachedCoachingSuggestions() async {
    try {
      await _ensureInitialized();
      final data =
          _storageService.getData<List<dynamic>>(_coachingSuggestionsKey) ?? [];
      return data
          .map(
            (json) => CoachingSuggestionModel.fromJson(
              Map<String, dynamic>.from(json),
            ),
          )
          .toList();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached coaching suggestions: $e',
      );
    }
  }

  // User Progress Analytics
  @override
  Future<void> cacheUserProgress(
    ProgressReportModel progress,
    String period,
  ) async {
    try {
      await _ensureInitialized();
      final key = '${_userProgressKey}_$period';
      await _storageService.setData(key, progress.toJson());
    } catch (e) {
      throw CacheException(message: 'Failed to cache user progress: $e');
    }
  }

  @override
  Future<ProgressReportModel?> getCachedUserProgress(String period) async {
    try {
      await _ensureInitialized();
      final key = '${_userProgressKey}_$period';
      final data = _storageService.getData<Map<String, dynamic>>(key);
      return data != null ? ProgressReportModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached user progress: $e');
    }
  }

  // Habit Analytics
  @override
  Future<void> cacheHabitAnalytics(
    HabitAnalyticsModel analytics,
    String period,
  ) async {
    try {
      await _ensureInitialized();
      final key = '${_habitAnalyticsKey}_$period';
      await _storageService.setData(key, analytics.toJson());
    } catch (e) {
      throw CacheException(message: 'Failed to cache habit analytics: $e');
    }
  }

  @override
  Future<HabitAnalyticsModel?> getCachedHabitAnalytics(String period) async {
    try {
      await _ensureInitialized();
      final key = '${_habitAnalyticsKey}_$period';
      final data = _storageService.getData<Map<String, dynamic>>(key);
      return data != null ? HabitAnalyticsModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached habit analytics: $e');
    }
  }

  // Productivity Analytics
  @override
  Future<void> cacheProductivityAnalytics(
    ProductivityAnalyticsModel analytics,
    String period,
  ) async {
    try {
      await _ensureInitialized();
      final key = '${_productivityAnalyticsKey}_$period';
      await _storageService.setData(key, analytics.toJson());
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache productivity analytics: $e',
      );
    }
  }

  @override
  Future<ProductivityAnalyticsModel?> getCachedProductivityAnalytics(
    String period,
  ) async {
    try {
      await _ensureInitialized();
      final key = '${_productivityAnalyticsKey}_$period';
      final data = _storageService.getData<Map<String, dynamic>>(key);
      return data != null ? ProductivityAnalyticsModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached productivity analytics: $e',
      );
    }
  }

  // Mood Analytics
  @override
  Future<void> cacheMoodAnalytics(
    MoodAnalyticsModel analytics,
    String period,
  ) async {
    try {
      await _ensureInitialized();
      final key = '${_moodAnalyticsKey}_$period';
      await _storageService.setData(key, analytics.toJson());
    } catch (e) {
      throw CacheException(message: 'Failed to cache mood analytics: $e');
    }
  }

  @override
  Future<MoodAnalyticsModel?> getCachedMoodAnalytics(String period) async {
    try {
      await _ensureInitialized();
      final key = '${_moodAnalyticsKey}_$period';
      final data = _storageService.getData<Map<String, dynamic>>(key);
      return data != null ? MoodAnalyticsModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached mood analytics: $e');
    }
  }

  // Personalized Insights
  @override
  Future<void> cachePersonalizedInsights(
    PersonalizedInsightsModel insights,
  ) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _personalizedInsightsKey,
        insights.toJson(),
      );
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache personalized insights: $e',
      );
    }
  }

  @override
  Future<PersonalizedInsightsModel?> getCachedPersonalizedInsights() async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<Map<String, dynamic>>(
        _personalizedInsightsKey,
      );
      return data != null ? PersonalizedInsightsModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached personalized insights: $e',
      );
    }
  }

  // Weekly Stats
  @override
  Future<void> cacheWeeklyStats(WeeklyStatsModel stats) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(_weeklyStatsKey, stats.toJson());
    } catch (e) {
      throw CacheException(message: 'Failed to cache weekly stats: $e');
    }
  }

  @override
  Future<WeeklyStatsModel?> getCachedWeeklyStats() async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<Map<String, dynamic>>(
        _weeklyStatsKey,
      );
      return data != null ? WeeklyStatsModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached weekly stats: $e');
    }
  }

  // Habit Correlations
  @override
  Future<void> cacheHabitCorrelations(
    MoodAnalyticsModel correlations,
    String period,
  ) async {
    try {
      await _ensureInitialized();
      final key = '${_habitCorrelationsKey}_$period';
      await _storageService.setData(key, correlations.toJson());
    } catch (e) {
      throw CacheException(message: 'Failed to cache habit correlations: $e');
    }
  }

  @override
  Future<MoodAnalyticsModel?> getCachedHabitCorrelations(String period) async {
    try {
      await _ensureInitialized();
      final key = '${_habitCorrelationsKey}_$period';
      final data = _storageService.getData<Map<String, dynamic>>(key);
      return data != null ? MoodAnalyticsModel.fromJson(data) : null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached habit correlations: $e',
      );
    }
  }

  // Clear All Cache
  @override
  Future<void> clearAllCache() async {
    try {
      await _ensureInitialized();

      // Clear all AI reports related cache
      await _storageService.removeData(_weeklyProgressReportKey);
      await _storageService.removeData(_coachingSuggestionsKey);
      await _storageService.removeData(_personalizedInsightsKey);
      await _storageService.removeData(_weeklyStatsKey);

      // Clear period-based cache (we need to clear all possible periods)
      final periods = ['day', 'week', 'month', 'year'];
      for (final period in periods) {
        await _storageService.removeData('${_userProgressKey}_$period');
        await _storageService.removeData('${_habitAnalyticsKey}_$period');
        await _storageService.removeData(
          '${_productivityAnalyticsKey}_$period',
        );
        await _storageService.removeData('${_moodAnalyticsKey}_$period');
        await _storageService.removeData('${_habitCorrelationsKey}_$period');
      }
    } catch (e) {
      throw CacheException(message: 'Failed to clear all cache: $e');
    }
  }
}
