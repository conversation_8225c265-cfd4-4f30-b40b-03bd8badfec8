import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_remote_data_source.dart';
import 'package:power_up/features/ai_reports/data/models/progress_report_model.dart';
import 'package:power_up/features/ai_reports/data/models/coaching_suggestion_model.dart';
import 'package:power_up/features/ai_reports/data/models/habit_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/productivity_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/mood_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/personalized_insights_model.dart';
import 'package:power_up/features/ai_reports/data/models/weekly_stats_model.dart';
import 'package:power_up/features/core/data/api/api_client.dart';

/// Implementation of the AiReportRemoteDataSource
class AiReportRemoteDataSourceImpl implements AiReportRemoteDataSource {
  final ApiClient _apiClient;

  AiReportRemoteDataSourceImpl({required ApiClient apiClient})
    : _apiClient = apiClient;

  @override
  Future<void> init() async {
    // No initialization needed
  }

  @override
  Future<void> dispose() async {
    // No cleanup needed
  }

  @override
  Future<T> request<T>({
    required String endpoint,
    required T Function(Map<String, dynamic> json) fromJson,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    String method = 'GET',
    Map<String, String>? headers,
  }) async {
    switch (method.toUpperCase()) {
      case 'GET':
        return _apiClient.get<T>(
          endpoint: endpoint,
          fromData: (data) => fromJson(data as Map<String, dynamic>),
          queryParameters: queryParameters,
          headers: headers,
        );
      case 'POST':
        return _apiClient.post<T>(
          endpoint: endpoint,
          fromData: (data) => fromJson(data as Map<String, dynamic>),
          data: body,
          queryParameters: queryParameters,
          headers: headers,
        );
      default:
        throw ServerException(message: 'Method $method not supported');
    }
  }

  @override
  Future<ProgressReportModel> getWeeklyProgressReport() async {
    return request<ProgressReportModel>(
      endpoint: '/analytics/progress',
      queryParameters: {'period': 'week'},
      fromJson: (json) => ProgressReportModel.fromJson(json),
    );
  }

  @override
  Future<List<CoachingSuggestionModel>> getCoachingSuggestions() async {
    return request<List<CoachingSuggestionModel>>(
      endpoint: '/analytics/insights',
      fromJson:
          (json) =>
              (json['recommendations'] as List)
                  .map(
                    (e) => CoachingSuggestionModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList(),
    );
  }

  @override
  Future<ProgressReportModel> getUserProgress({required String period}) async {
    return request<ProgressReportModel>(
      endpoint: '/analytics/progress',
      queryParameters: {'period': period},
      fromJson: (json) => ProgressReportModel.fromJson(json),
    );
  }

  @override
  Future<HabitAnalyticsModel> getHabitAnalytics({
    required String period,
  }) async {
    return request<HabitAnalyticsModel>(
      endpoint: '/analytics/habits',
      queryParameters: {'period': period},
      fromJson: (json) => HabitAnalyticsModel.fromJson(json),
    );
  }

  @override
  Future<ProductivityAnalyticsModel> getProductivityAnalytics({
    required String period,
  }) async {
    return request<ProductivityAnalyticsModel>(
      endpoint: '/analytics/productivity',
      queryParameters: {'period': period},
      fromJson: (json) => ProductivityAnalyticsModel.fromJson(json),
    );
  }

  @override
  Future<MoodAnalyticsModel> getMoodAnalytics({required String period}) async {
    return request<MoodAnalyticsModel>(
      endpoint: '/analytics/mood',
      queryParameters: {'period': period},
      fromJson: (json) => MoodAnalyticsModel.fromJson(json),
    );
  }

  @override
  Future<PersonalizedInsightsModel> getPersonalizedInsights() async {
    return request<PersonalizedInsightsModel>(
      endpoint: '/analytics/insights',
      fromJson: (json) => PersonalizedInsightsModel.fromJson(json),
    );
  }

  @override
  Future<WeeklyStatsModel> getWeeklyStats() async {
    return request<WeeklyStatsModel>(
      endpoint: '/analytics/weekly-stats',
      fromJson: (json) => WeeklyStatsModel.fromJson(json),
    );
  }

  @override
  Future<MoodAnalyticsModel> getHabitCorrelations({
    required String period,
  }) async {
    return request<MoodAnalyticsModel>(
      endpoint: '/analytics/habit-correlations',
      queryParameters: {'period': period},
      fromJson: (json) => MoodAnalyticsModel.fromJson(json),
    );
  }

  @override
  Future<void> recordMoodEntry({required String mood}) async {
    await _apiClient.post<void>(
      endpoint: '/analytics/mood',
      data: {'mood': mood},
      fromData: (_) {},
    );
  }

  @override
  Future<void> recordFocusSession({required int minutes}) async {
    await _apiClient.post<void>(
      endpoint: '/analytics/focus-session',
      data: {'minutes': minutes},
      fromData: (_) {},
    );
  }
}
