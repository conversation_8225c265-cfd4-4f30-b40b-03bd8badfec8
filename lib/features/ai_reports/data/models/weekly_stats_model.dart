import 'package:power_up/features/ai_reports/domain/entities/weekly_stats_entity.dart';

/// Model for daily stats
class DailyStatsModel extends DailyStatsEntity {
  const DailyStatsModel({
    required super.date,
    required super.tasksCompleted,
    required super.habitsCompleted,
    required super.focusMinutes,
    required super.productivityScore,
  });

  factory DailyStatsModel.fromJson(Map<String, dynamic> json) {
    return DailyStatsModel(
      date: DateTime.parse(json['date'] as String),
      tasksCompleted: (json['tasksCompleted'] as num).toInt(),
      habitsCompleted: (json['habitsCompleted'] as num).toInt(),
      focusMinutes: (json['focusMinutes'] as num).toDouble(),
      productivityScore: (json['productivityScore'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'tasksCompleted': tasksCompleted,
      'habitsCompleted': habitsCompleted,
      'focusMinutes': focusMinutes,
      'productivityScore': productivityScore,
    };
  }

  factory DailyStatsModel.fromEntity(DailyStatsEntity entity) {
    return DailyStatsModel(
      date: entity.date,
      tasksCompleted: entity.tasksCompleted,
      habitsCompleted: entity.habitsCompleted,
      focusMinutes: entity.focusMinutes,
      productivityScore: entity.productivityScore,
    );
  }
}

/// Model for weekly stats
class WeeklyStatsModel extends WeeklyStatsEntity {
  const WeeklyStatsModel({
    required super.dailyStats,
    required super.totalTasks,
    required super.totalHabits,
    required super.totalFocusMinutes,
    required super.weeklyProductivityScore,
    required super.mostProductiveDay,
  });

  factory WeeklyStatsModel.fromJson(Map<String, dynamic> json) {
    return WeeklyStatsModel(
      dailyStats:
          (json['dailyStats'] as List<dynamic>)
              .map(
                (item) =>
                    DailyStatsModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      totalTasks: (json['totalTasks'] as num).toInt(),
      totalHabits: (json['totalHabits'] as num).toInt(),
      totalFocusMinutes: (json['totalFocusMinutes'] as num).toDouble(),
      weeklyProductivityScore:
          (json['weeklyProductivityScore'] as num).toDouble(),
      mostProductiveDay: json['mostProductiveDay'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dailyStats':
          dailyStats
              .map((item) => DailyStatsModel.fromEntity(item).toJson())
              .toList(),
      'totalTasks': totalTasks,
      'totalHabits': totalHabits,
      'totalFocusMinutes': totalFocusMinutes,
      'weeklyProductivityScore': weeklyProductivityScore,
      'mostProductiveDay': mostProductiveDay,
    };
  }

  factory WeeklyStatsModel.fromEntity(WeeklyStatsEntity entity) {
    return WeeklyStatsModel(
      dailyStats: entity.dailyStats,
      totalTasks: entity.totalTasks,
      totalHabits: entity.totalHabits,
      totalFocusMinutes: entity.totalFocusMinutes,
      weeklyProductivityScore: entity.weeklyProductivityScore,
      mostProductiveDay: entity.mostProductiveDay,
    );
  }
}
