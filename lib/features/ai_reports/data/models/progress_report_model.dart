import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';

/// Model for daily progress
class DailyProgressModel extends DailyProgressEntity {
  const DailyProgressModel({
    required super.date,
    required super.tasksCompleted,
    required super.habitsCompleted,
    required super.productivityScore,
    required super.mood,
  });

  factory DailyProgressModel.fromJson(Map<String, dynamic> json) {
    return DailyProgressModel(
      date: DateTime.parse(json['date'] as String),
      tasksCompleted: (json['tasksCompleted'] as num).toInt(),
      habitsCompleted: (json['habitsCompleted'] as num).toInt(),
      productivityScore: (json['productivityScore'] as num).toDouble(),
      mood: json['mood'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'tasksCompleted': tasksCompleted,
      'habitsCompleted': habitsCompleted,
      'productivityScore': productivityScore,
      'mood': mood,
    };
  }

  factory DailyProgressModel.fromEntity(DailyProgressEntity entity) {
    return DailyProgressModel(
      date: entity.date,
      tasksCompleted: entity.tasksCompleted,
      habitsCompleted: entity.habitsCompleted,
      productivityScore: entity.productivityScore,
      mood: entity.mood,
    );
  }
}

/// Model for progress report
class ProgressReportModel extends ProgressReportEntity {
  const ProgressReportModel({
    required super.id,
    required super.summary,
    required super.insights,
    required super.tips,
    required super.period,
    required super.overallScore,
    required super.dailyProgress,
    required super.totalTasksCompleted,
    required super.totalHabitsCompleted,
    required super.changeFromPreviousPeriod,
    required super.generatedAt,
  });

  factory ProgressReportModel.fromJson(Map<String, dynamic> json) {
    return ProgressReportModel(
      id: json['id'] as String,
      summary: json['summary'] as String,
      insights: List<String>.from(json['insights'] as List<dynamic>),
      tips: List<String>.from(json['tips'] as List<dynamic>),
      period: json['period'] as String,
      overallScore: (json['overallScore'] as num).toDouble(),
      dailyProgress:
          (json['dailyProgress'] as List<dynamic>)
              .map(
                (item) =>
                    DailyProgressModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      totalTasksCompleted: (json['totalTasksCompleted'] as num).toInt(),
      totalHabitsCompleted: (json['totalHabitsCompleted'] as num).toInt(),
      changeFromPreviousPeriod:
          (json['changeFromPreviousPeriod'] as num).toDouble(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'summary': summary,
      'insights': insights,
      'tips': tips,
      'period': period,
      'overallScore': overallScore,
      'dailyProgress':
          dailyProgress
              .map((item) => DailyProgressModel.fromEntity(item).toJson())
              .toList(),
      'totalTasksCompleted': totalTasksCompleted,
      'totalHabitsCompleted': totalHabitsCompleted,
      'changeFromPreviousPeriod': changeFromPreviousPeriod,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory ProgressReportModel.fromEntity(ProgressReportEntity entity) {
    return ProgressReportModel(
      id: entity.id,
      summary: entity.summary,
      insights: entity.insights,
      tips: entity.tips,
      period: entity.period,
      overallScore: entity.overallScore,
      dailyProgress: entity.dailyProgress,
      totalTasksCompleted: entity.totalTasksCompleted,
      totalHabitsCompleted: entity.totalHabitsCompleted,
      changeFromPreviousPeriod: entity.changeFromPreviousPeriod,
      generatedAt: entity.generatedAt,
    );
  }
}
