import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';

/// Model for coaching suggestion
class CoachingSuggestionModel extends CoachingSuggestionEntity {
  const CoachingSuggestionModel({
    required super.id,
    required super.suggestionText,
    super.relatedHabitId,
    super.relatedTaskId,
    required super.impactArea,
    required super.priority,
    required super.type,
    required super.createdAt,
  });

  factory CoachingSuggestionModel.fromJson(Map<String, dynamic> json) {
    return CoachingSuggestionModel(
      id: json['id'] as String,
      suggestionText: json['suggestionText'] as String,
      relatedHabitId: json['relatedHabitId'] as String?,
      relatedTaskId: json['relatedTaskId'] as String?,
      impactArea: json['impactArea'] as String,
      priority: json['priority'] as String,
      type: json['type'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'suggestionText': suggestionText,
      'relatedHabitId': relatedHabitId,
      'relatedTaskId': relatedTaskId,
      'impactArea': impactArea,
      'priority': priority,
      'type': type,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory CoachingSuggestionModel.fromEntity(CoachingSuggestionEntity entity) {
    return CoachingSuggestionModel(
      id: entity.id,
      suggestionText: entity.suggestionText,
      relatedHabitId: entity.relatedHabitId,
      relatedTaskId: entity.relatedTaskId,
      impactArea: entity.impactArea,
      priority: entity.priority,
      type: entity.type,
      createdAt: entity.createdAt,
    );
  }
}
