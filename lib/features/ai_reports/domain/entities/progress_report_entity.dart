import 'package:equatable/equatable.dart';

class ProgressReportEntity extends Equatable {
  final String id;
  final String summary;
  final List<String> insights;
  final List<String> tips;
  final String period;
  final double overallScore;
  final List<DailyProgressEntity> dailyProgress;
  final int totalTasksCompleted;
  final int totalHabitsCompleted;
  final double changeFromPreviousPeriod;
  final DateTime generatedAt;

  const ProgressReportEntity({
    required this.id,
    required this.summary,
    required this.insights,
    required this.tips,
    required this.period,
    required this.overallScore,
    required this.dailyProgress,
    required this.totalTasksCompleted,
    required this.totalHabitsCompleted,
    required this.changeFromPreviousPeriod,
    required this.generatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    summary,
    insights,
    tips,
    period,
    overallScore,
    dailyProgress,
    totalTasksCompleted,
    totalHabitsCompleted,
    changeFromPreviousPeriod,
    generatedAt,
  ];
}

class DailyProgressEntity extends Equatable {
  final DateTime date;
  final int tasksCompleted;
  final int habitsCompleted;
  final double productivityScore;
  final String mood;

  const DailyProgressEntity({
    required this.date,
    required this.tasksCompleted,
    required this.habitsCompleted,
    required this.productivityScore,
    required this.mood,
  });

  @override
  List<Object?> get props => [
    date,
    tasksCompleted,
    habitsCompleted,
    productivityScore,
    mood,
  ];
}
