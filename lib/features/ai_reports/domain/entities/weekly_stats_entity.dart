import 'package:equatable/equatable.dart';

class WeeklyStatsEntity extends Equatable {
  final List<DailyStatsEntity>? dailyStats;
  final int totalTasks;
  final int totalHabits;
  final double totalFocusMinutes;
  final double weeklyProductivityScore;
  final String mostProductiveDay;

  const WeeklyStatsEntity({
    required this.dailyStats,
    required this.totalTasks,
    required this.totalHabits,
    required this.totalFocusMinutes,
    required this.weeklyProductivityScore,
    required this.mostProductiveDay,
  });

  @override
  List<Object?> get props => [
    dailyStats,
    totalTasks,
    totalHabits,
    totalFocusMinutes,
    weeklyProductivityScore,
    mostProductiveDay,
  ];
}

class DailyStatsEntity extends Equatable {
  final String day;
  final int tasksCompleted;
  final int habitsCompleted;
  final double focusMinutes;
  final double productivityScore;

  const DailyStatsEntity({
    required this.day,
    required this.tasksCompleted,
    required this.habitsCompleted,
    required this.focusMinutes,
    required this.productivityScore,
  });

  @override
  List<Object?> get props => [
    day,
    tasksCompleted,
    habitsCompleted,
    focusMinutes,
    productivityScore,
  ];
}
