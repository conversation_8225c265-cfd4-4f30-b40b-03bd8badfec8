import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/ai_improvement_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_adapter.dart';
import 'package:power_up/features/ai_reports/domain/entities/weekly_stats_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/goal_entity.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_weekly_progress_report_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_ai_improvement_report_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_weekly_stats_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_user_progress_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/record_mood_entry_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/create_goal_usecase.dart';

/// Controller for AI Reports feature
class AiReportsController extends GetxController {
  final GetWeeklyProgressReportUseCase _getWeeklyProgressReportUseCase;
  final GetAIImprovementReportUseCase _getAIImprovementReportUseCase;
  final GetWeeklyStatsUseCase _getWeeklyStatsUseCase;
  final GetUserProgressUseCase _getUserProgressUseCase;
  final RecordMoodEntryUseCase _recordMoodEntryUseCase;
  final CreateGoalUseCase _createGoalUseCase;

  // Reactive state variables
  final _isLoading = false.obs;
  final _progressReport = Rxn<ProgressReportEntity>();
  final _aiImprovementReport = Rxn<AIImprovementReportEntity>();
  final _weeklyStats = Rxn<WeeklyStatsEntity>();
  final _errorMessage = ''.obs;
  final _selectedPeriod = 'week'.obs;
  final _currentMood = ''.obs;
  final _isMoodRecording = false.obs;
  final _goals = <GoalEntity>[].obs;
  final _isCreatingGoal = false.obs;

  AiReportsController({
    required GetWeeklyProgressReportUseCase getWeeklyProgressReportUseCase,
    required GetAIImprovementReportUseCase getAIImprovementReportUseCase,
    required GetWeeklyStatsUseCase getWeeklyStatsUseCase,
    required GetUserProgressUseCase getUserProgressUseCase,
    required RecordMoodEntryUseCase recordMoodEntryUseCase,
    required CreateGoalUseCase createGoalUseCase,
  }) : _getWeeklyProgressReportUseCase = getWeeklyProgressReportUseCase,
       _getAIImprovementReportUseCase = getAIImprovementReportUseCase,
       _getWeeklyStatsUseCase = getWeeklyStatsUseCase,
       _getUserProgressUseCase = getUserProgressUseCase,
       _recordMoodEntryUseCase = recordMoodEntryUseCase,
       _createGoalUseCase = createGoalUseCase;

  // Getters
  bool get isLoading => _isLoading.value;
  ProgressReportEntity? get progressReport => _progressReport.value;
  AIImprovementReportEntity? get aiImprovementReport =>
      _aiImprovementReport.value;
  WeeklyStatsEntity? get weeklyStats => _weeklyStats.value;

  /// Backward compatibility getter for coaching suggestions
  List<CoachingSuggestionEntity> get coachingSuggestions {
    final report = _aiImprovementReport.value;
    if (report == null) return [];

    // Extract all actions from improvement areas and convert to coaching suggestions
    final actions =
        report.improvements
            .expand((improvement) => improvement.actions)
            .toList();

    return CoachingSuggestionAdapter.fromActions(actions);
  }

  String get errorMessage => _errorMessage.value;
  String get selectedPeriod => _selectedPeriod.value;
  String get currentMood => _currentMood.value;
  bool get isMoodRecording => _isMoodRecording.value;
  List<GoalEntity> get goals => _goals;
  bool get isCreatingGoal => _isCreatingGoal.value;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  /// Load initial data when controller is initialized
  Future<void> loadInitialData() async {
    await Future.wait([
      loadWeeklyProgressReport(),
      loadCoachingSuggestions(),
      loadWeeklyStats(),
    ]);
  }

  /// Load weekly progress report
  Future<void> loadWeeklyProgressReport() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _getWeeklyProgressReportUseCase(NoParams());

      result.fold(
        (failure) => _handleFailure(failure),
        (report) => _progressReport.value = report,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load AI improvement report
  Future<void> loadAIImprovementReport({int days = 30}) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _getAIImprovementReportUseCase(
        GetAIImprovementReportParams(days: days),
      );

      result.fold(
        (failure) => _handleFailure(failure),
        (report) => _aiImprovementReport.value = report,
      );
    } catch (e) {
      _handleError('Failed to load AI improvement report: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load coaching suggestions (backward compatibility)
  Future<void> loadCoachingSuggestions() async {
    await loadAIImprovementReport();
  }

  /// Load weekly statistics
  Future<void> loadWeeklyStats() async {
    try {
      final result = await _getWeeklyStatsUseCase(NoParams());

      result.fold(
        (failure) => _handleFailure(failure),
        (stats) => _weeklyStats.value = stats,
      );
    } catch (e) {
      _handleError('Failed to load weekly stats: $e');
    }
  }

  /// Load user progress for specific period
  Future<void> loadUserProgress(String period) async {
    try {
      _isLoading.value = true;
      _selectedPeriod.value = period;
      _errorMessage.value = '';

      final params = GetUserProgressParams(period: period);
      final result = await _getUserProgressUseCase(params);

      result.fold(
        (failure) => _handleFailure(failure),
        (report) => _progressReport.value = report,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Change the selected period and reload data
  Future<void> changePeriod(String period) async {
    if (_selectedPeriod.value != period) {
      await loadUserProgress(period);
    }
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await loadInitialData();
  }

  /// Get productivity trend data for charts
  List<Map<String, dynamic>> getProductivityTrendData() {
    if (_progressReport.value == null) return [];

    return _progressReport.value!.dailyProgress.map((daily) {
      return {
        'date': daily.date,
        'score': daily.score,
        'tasks': daily.tasksCompleted,
        'habits': daily.habitsCompleted,
      };
    }).toList();
  }

  /// Get task completion data for charts
  Map<String, int> getTaskHabitCompletionData() {
    if (_progressReport.value == null) {
      return {'tasks': 0, 'habits': 0};
    }

    return {
      'tasks': _progressReport.value!.totalTasksCompleted,
      'habits': _progressReport.value!.totalHabitsCompleted,
    };
  }

  /// Get weekly overview data
  Map<String, dynamic> getWeeklyOverview() {
    if (_weeklyStats.value == null) {
      return {
        'totalTasks': 0,
        'totalHabits': 0,
        'totalFocusMinutes': 0.0,
        'averageProductivity': 0.0,
        'mostProductiveDay': 'N/A',
      };
    }

    return {
      'totalTasks': _weeklyStats.value!.totalTasks,
      'totalHabits': _weeklyStats.value!.totalHabits,
      'totalFocusMinutes': _weeklyStats.value!.totalFocusMinutes,
      'averageProductivity': _weeklyStats.value!.weeklyProductivityScore,
      'mostProductiveDay': _weeklyStats.value!.mostProductiveDay,
    };
  }

  /// Get coaching suggestions by priority (backward compatibility)
  Map<String, List<CoachingSuggestionEntity>> getSuggestionsByPriority() {
    final suggestions = coachingSuggestions;
    return {
      'high': suggestions.where((s) => s.priority == 'high').toList(),
      'medium': suggestions.where((s) => s.priority == 'medium').toList(),
      'low': suggestions.where((s) => s.priority == 'low').toList(),
    };
  }

  /// Handle failures from use cases
  void _handleFailure(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        _errorMessage.value = 'Server error: ${failure.message}';
        break;
      case NetworkFailure:
        _errorMessage.value = 'Network error: Please check your connection';
        break;
      case CacheFailure:
        _errorMessage.value = 'Data unavailable offline';
        break;
      default:
        _errorMessage.value =
            'An unexpected error occurred: ${failure.message}';
    }
  }

  /// Handle general errors
  void _handleError(String message) {
    _errorMessage.value = message;
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Record user mood entry
  Future<void> recordMoodEntry(String mood) async {
    try {
      _isMoodRecording.value = true;
      _errorMessage.value = '';

      final params = RecordMoodEntryParams(mood: mood);
      final result = await _recordMoodEntryUseCase(params);

      result.fold((failure) => _handleFailure(failure), (_) {
        _currentMood.value = mood;
        Get.snackbar(
          'Success',
          'Mood recorded successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      });
    } finally {
      _isMoodRecording.value = false;
    }
  }

  /// Get available mood options
  List<Map<String, dynamic>> getMoodOptions() {
    return [
      {'value': 'great', 'label': '😄 Great', 'color': const Color(0xFF4CAF50)},
      {'value': 'good', 'label': '🙂 Good', 'color': const Color(0xFF8BC34A)},
      {
        'value': 'neutral',
        'label': '😐 Neutral',
        'color': const Color(0xFFFF9800),
      },
      {'value': 'bad', 'label': '🙁 Bad', 'color': const Color(0xFFFF5722)},
      {
        'value': 'terrible',
        'label': '😩 Terrible',
        'color': const Color(0xFFE53935),
      },
    ];
  }

  /// Create a new goal
  Future<void> createGoal({
    required String title,
    required String description,
    required String category,
    required String priority,
    required DateTime targetDate,
  }) async {
    try {
      _isCreatingGoal.value = true;
      _errorMessage.value = '';

      final params = CreateGoalParams(
        title: title,
        description: description,
        category: category,
        priority: priority,
        targetDate: targetDate,
      );
      final result = await _createGoalUseCase(params);

      result.fold((failure) => _handleFailure(failure), (goal) {
        _goals.add(goal);
        Get.snackbar(
          'Success',
          'Goal created successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      });
    } finally {
      _isCreatingGoal.value = false;
    }
  }

  /// Get available goal categories
  List<Map<String, dynamic>> getGoalCategories() {
    return [
      {
        'value': 'health',
        'label': '🏃 Health',
        'color': const Color(0xFF4CAF50),
      },
      {
        'value': 'career',
        'label': '💼 Career',
        'color': const Color(0xFF2196F3),
      },
      {
        'value': 'personal',
        'label': '🌱 Personal',
        'color': const Color(0xFF9C27B0),
      },
      {
        'value': 'education',
        'label': '📚 Education',
        'color': const Color(0xFFFF9800),
      },
      {
        'value': 'finance',
        'label': '💰 Finance',
        'color': const Color(0xFF4CAF50),
      },
    ];
  }

  /// Get available goal priorities
  List<Map<String, dynamic>> getGoalPriorities() {
    return [
      {'value': 'low', 'label': 'Low', 'color': const Color(0xFF8BC34A)},
      {'value': 'medium', 'label': 'Medium', 'color': const Color(0xFFFF9800)},
      {'value': 'high', 'label': 'High', 'color': const Color(0xFFE53935)},
    ];
  }

  /// Get goals by category
  Map<String, List<GoalEntity>> getGoalsByCategory() {
    final goalsList = _goals;
    return {
      'health': goalsList.where((g) => g.category == 'health').toList(),
      'career': goalsList.where((g) => g.category == 'career').toList(),
      'personal': goalsList.where((g) => g.category == 'personal').toList(),
      'education': goalsList.where((g) => g.category == 'education').toList(),
      'finance': goalsList.where((g) => g.category == 'finance').toList(),
    };
  }

  /// Get goals by priority
  Map<String, List<GoalEntity>> getGoalsByPriority() {
    final goalsList = _goals;
    return {
      'high': goalsList.where((g) => g.priority == 'high').toList(),
      'medium': goalsList.where((g) => g.priority == 'medium').toList(),
      'low': goalsList.where((g) => g.priority == 'low').toList(),
    };
  }

  /// Get active goals (not completed)
  List<GoalEntity> getActiveGoals() {
    return _goals.where((goal) => !goal.isCompleted).toList();
  }

  /// Get completed goals
  List<GoalEntity> getCompletedGoals() {
    return _goals.where((goal) => goal.isCompleted).toList();
  }
}
